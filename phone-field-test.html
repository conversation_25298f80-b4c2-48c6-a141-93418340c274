<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقل الهاتف - RID COD</title>
    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        
        .field-group {
            margin-bottom: 15px;
            position: relative;
        }
        
        /* Apply the same fixes as in the main CSS */
        input[type="tel"] {
            direction: rtl !important;
            text-align: right !important;
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="tel"]::placeholder {
            direction: rtl !important;
            text-align: right !important;
            unicode-bidi: plaintext !important;
        }
        
        /* WebKit browsers */
        input[type="tel"]::-webkit-input-placeholder {
            direction: rtl !important;
            text-align: right !important;
            unicode-bidi: plaintext !important;
        }
        
        /* Mozilla Firefox */
        input[type="tel"]::-moz-placeholder {
            direction: rtl !important;
            text-align: right !important;
            unicode-bidi: plaintext !important;
        }
        
        /* Microsoft Edge */
        input[type="tel"]::-ms-input-placeholder {
            direction: rtl !important;
            text-align: right !important;
            unicode-bidi: plaintext !important;
        }
        
        input[type="text"] {
            direction: rtl;
            text-align: right;
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        
        .before {
            background: #ffe6e6;
            border: 1px solid #ffcccc;
        }
        
        .after {
            background: #e6ffe6;
            border: 1px solid #ccffcc;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح حقل الهاتف - RID COD</h1>
        <p>هذه الصفحة تختبر ما إذا كان النص الافتراضي (placeholder) في حقل الهاتف يظهر في الجهة اليمنى بشكل صحيح.</p>
        
        <div class="test-section">
            <h3>مقارنة الحقول</h3>
            <div class="comparison">
                <div class="before">
                    <h4>حقل نص عادي (للمقارنة)</h4>
                    <div class="field-group">
                        <input type="text" placeholder="الاسم الكامل" />
                    </div>
                    <p><small>هذا حقل نص عادي - يجب أن يظهر النص في اليمين</small></p>
                </div>
                
                <div class="after">
                    <h4>حقل الهاتف (بعد الإصلاح)</h4>
                    <div class="field-group">
                        <input type="tel" placeholder="رقم الهاتف" />
                    </div>
                    <p><small>هذا حقل الهاتف - يجب أن يظهر النص في اليمين أيضاً</small></p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>اختبار الكتابة</h3>
            <div class="field-group">
                <label>اكتب رقم هاتف للاختبار:</label>
                <input type="tel" id="phone-test" placeholder="رقم الهاتف" />
            </div>
            <div id="test-result" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>تعليمات الاختبار</h3>
            <ol>
                <li>تحقق من أن النص الافتراضي "رقم الهاتف" يظهر في الجهة اليمنى</li>
                <li>اكتب رقم هاتف في الحقل وتأكد من أن النص يظهر من اليمين</li>
                <li>قارن مع حقل الاسم للتأكد من التطابق</li>
                <li>إذا كان كل شيء يعمل بشكل صحيح، فالإصلاح نجح!</li>
            </ol>
        </div>
    </div>
    
    <script>
        document.getElementById('phone-test').addEventListener('input', function() {
            const value = this.value;
            const resultDiv = document.getElementById('test-result');
            
            if (value.length > 0) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'status success';
                resultDiv.textContent = '✅ النص يظهر بشكل صحيح! الإصلاح يعمل.';
            } else {
                resultDiv.style.display = 'none';
            }
        });
    </script>
</body>
</html>
