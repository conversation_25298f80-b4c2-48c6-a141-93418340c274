/* Reset and Base Styles */
:root {
    --rid-cod-primary-bg-color: #ffffff; /* Default Primary Background */
    --rid-cod-button-bg-color: #6a3de8; /* Default Button Background */
    --rid-cod-button-text-color: #ffffff; /* Default Button Text */
    --rid-cod-accent-color: #6a3de8; /* Default Accent Color - will be overridden by form style */
    --rid-cod-accent-color-rgb: 106, 61, 232; /* RGB values for accent color */
    --rid-cod-border-color: #e0e0e0; /* Default Border Color */
    --rid-cod-text-color: #333333; /* Default Text Color */
    --rid-cod-label-color: #555555; /* Default Label Color */
    --rid-cod-error-color: #dc3232; /* Default Error Color */
    --rid-cod-success-color: #46b450; /* Default Success Color */

    /* Sticky Button Colors */
    --rid-cod-sticky-button-bg-color: #6a3de8; /* Default Sticky Button Background */
}

/* Third Form Default Colors - Override when third form is active */
.rid-cod-form-third {
    --rid-cod-accent-color: #c53030; /* Default red color for third form */
    --rid-cod-accent-color-rgb: 197, 48, 48; /* RGB values for third form red */
    --rid-cod-sticky-button-text-color: #ffffff; /* Default Sticky Button Text */
    --rid-cod-sticky-button-border-color: #6a3de8; /* Default Sticky Button Border */
}

#rid-cod-checkout *,
#rid-cod-checkout *:before,
#rid-cod-checkout *:after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* Main container styles */
#rid-cod-checkout {
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    padding: 25px;
    border-radius: 8px; /* Slightly less rounded */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07); /* Softer shadow */
    margin-bottom: 30px;
    max-width: 100%;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif; /* Prioritize Cairo if available */
    direction: rtl;
    border-top: 4px solid var(--rid-cod-accent-color); /* Use CSS Variable */
}

/* Title Styles */
.rid-cod-title h3 {
    font-size: 18px; /* Smaller title */
    margin-bottom: 25px;
    text-align: center;
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    font-weight: 700; /* Bolder */
    position: relative;
    padding-bottom: 0; /* Remove padding */
}

.rid-cod-title h3:after {
    display: none; /* Remove underline */
}

.form-title-icon {
    display: inline-block;
    margin-right: 5px; /* Space before icon */
}

/* Form layout */
#rid-cod-form {
    display: flex;
    flex-direction: column;
    grid-gap: 15px; /* Reduced gap */
}

.rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px; /* Reduced gap */
    margin-bottom: 0; /* Remove bottom margin */
}

/* Field group and input styles */
.rid-cod-field-group {
    margin-bottom: 0; /* Remove bottom margin */
    position: relative; /* Needed for icon positioning */
}

.rid-cod-field-group label {
    display: none; /* Hide labels as per design */
}

#rid-cod-form input[type="text"],
#rid-cod-form input[type="tel"],
#rid-cod-form input[type="email"],
#rid-cod-form select,
#rid-cod-form textarea {
    width: 100%;
    padding: 12px 40px 12px 15px; /* Adjust padding for icon */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-radius: 6px; /* Less rounded */
    font-size: 14px; /* Slightly smaller font */
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-shadow: none; /* Remove inner shadow */
    height: 48px; /* Fixed height */
}

/* Third Form Style - Modern Card Design */
.rid-cod-form-third #rid-cod-checkout {
    background: #ffffff;
    border: 2px solid var(--rid-cod-accent-color);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(var(--rid-cod-accent-color-rgb), 0.15);
    max-width: 500px;
    margin: 0 auto;
    direction: rtl;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
}

.rid-cod-form-third .rid-cod-title h3 {
    text-align: center;
    color: #2d3748;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 25px;
    border-bottom: none;
    padding-bottom: 0;
}

/* Hide default form elements for third form */
.rid-cod-form-third #rid-cod-summary-wrapper,
.rid-cod-form-third .rid-cod-actions-row,
.rid-cod-form-third .rid-cod-whatsapp-section {
    display: none !important;
}

/* External variations for third form (outside the form completely) */
.rid-cod-variations-external {
    margin-bottom: 30px;
    padding: 25px;
    background: #ffffff;
    border-radius: 15px;
    border: 2px solid var(--rid-cod-accent-color);
    box-shadow: 0 8px 25px rgba(var(--rid-cod-accent-color-rgb), 0.15);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    direction: rtl;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rid-cod-variations-external .variations-title {
    margin: 0 0 20px 0;
    padding: 0;
    font-size: 18px;
    font-weight: 700;
    color: var(--rid-cod-accent-color);
    text-align: center;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    display: block !important;
}

/* Ensure variations container is visible */
.rid-cod-variations-external .variations_form {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rid-cod-variations-external .variations-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure variation rows and content are visible */
.rid-cod-variations-external .variation-row {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rid-cod-variations-external .variation-label {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rid-cod-variations-external .variation-boxes {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.rid-cod-variations-external .variation-option {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Hide internal variations for third form (but keep external variations visible) */
.rid-cod-form-third .rid-cod-variations:not(.rid-cod-variations-external) {
    display: none !important;
}

/* Ensure external variations are never hidden and use classic form styling */
.rid-cod-variations-external {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    /* Inherit classic form variation styling */
    background: var(--rid-cod-primary-bg-color);
    border: 1px solid var(--rid-cod-border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

/* Override any potential hiding styles */
.rid-cod-variations-external * {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure variations appear first in third form */
.rid-cod-form-third #rid-cod-form {
    display: flex;
    flex-direction: column;
}

.rid-cod-form-third .rid-cod-variations {
    order: -2;
}

.rid-cod-form-third .rid-cod-customer-info {
    order: -1;
}

/* Third form variations - inherit classic form styling */
.rid-cod-form-third .rid-cod-variations-external .variation-row {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--rid-cod-border-color);
}

.rid-cod-form-third .rid-cod-variations-external .variation-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rid-cod-form-third .rid-cod-variations-external .attribute-label {
    display: block;
    font-weight: 600;
    color: var(--rid-cod-label-color);
    margin-bottom: 12px;
    font-size: 15px;
    text-align: right;
}

.rid-cod-form-third .rid-cod-variations-external .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
    direction: rtl;
}

/* Third form variation options - use classic form styling */
.rid-cod-form-third .rid-cod-variations-external .variation-option {
    padding: 10px 15px;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 6px;
    background: var(--rid-cod-primary-bg-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: var(--rid-cod-text-color);
    min-width: 45px;
    text-align: center;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Third form variation option states - use classic form behavior with CSS variables */
.rid-cod-form-third .rid-cod-variations-external .variation-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(var(--rid-cod-accent-color-rgb), 0.05);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(var(--rid-cod-accent-color-rgb), 0.15);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(var(--rid-cod-accent-color-rgb), 0.25);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
    border-color: var(--rid-cod-border-color);
    color: #6c757d;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.disabled:hover {
    transform: none;
    box-shadow: none;
    border-color: var(--rid-cod-border-color);
    background: #f8f9fa;
}

/* Disabled color options for third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.disabled {
    opacity: 0.25 !important;
    filter: grayscale(100%) brightness(1.2);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.disabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    z-index: 1;
}

/* Color options for third form - inherit classic form styling */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid var(--rid-cod-border-color);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 50% !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    display: block !important;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.selected {
    box-shadow: 0 0 0 3px var(--rid-cod-accent-color), 0 3px 8px rgba(var(--rid-cod-accent-color-rgb), 0.25);
    border-color: var(--rid-cod-accent-color);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Special handling for white/light colors in third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style*="background-color: #fff"],
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style*="background-color: #ffffff"],
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style*="background-color: rgb(255, 255, 255)"] {
    border: 2px solid #ddd !important;
}

/* Checkmark for selected colors in third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.selected:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.9;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5));
    z-index: 2;
}

/* Invert checkmark for light colors in third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.selected .color-swatch[style*="background-color: #fff"] ~ :after,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.selected .color-swatch[style*="background-color: #ffffff"] ~ :after,
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.selected .color-swatch[style*="background-color: #f5f5dc"] ~ :after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    opacity: 0.7;
}

/* Color names display for third form - enhanced to show both color and name */
.rid-cod-form-third.rid-cod-show-color-names .rid-cod-variations-external .variation-option.color-option {
    width: auto;
    height: 40px;
    border-radius: 20px;
    padding: 5px 15px 5px 45px;
    min-width: 80px;
    display: flex;
    align-items: center;
    position: relative;
    background: transparent !important;
    border: 2px solid var(--rid-cod-border-color);
}

.rid-cod-form-third.rid-cod-show-color-names .rid-cod-variations-external .variation-option.color-option .color-swatch {
    display: block !important;
    position: absolute !important;
    right: 5px !important;
    top: 5px !important;
    width: 30px !important;
    height: 30px !important;
    border-radius: 50% !important;
    left: auto !important;
    bottom: auto !important;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--rid-cod-text-color);
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 5px;
}

/* When color names are disabled, hide the text and show only color swatch */
.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .variation-option.color-option .color-name {
    display: none;
}

.rid-cod-form-third:not(.rid-cod-show-color-names) .rid-cod-variations-external .variation-option.color-option {
    width: 35px;
    height: 35px;
    padding: 0;
    border-radius: 50%;
}

/* Dropdown variations support for third form */
.rid-cod-form-third.rid-cod-use-dropdown-variations .rid-cod-variations-external .variation-boxes {
    display: none !important;
}

.rid-cod-form-third.rid-cod-use-dropdown-variations .rid-cod-variations-external select.rid-cod-variation-select {
    display: block !important;
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 8px;
    background: var(--rid-cod-primary-bg-color);
    color: var(--rid-cod-text-color);
    font-size: 14px;
    font-family: inherit;
    transition: all 0.3s ease;
}

.rid-cod-form-third.rid-cod-use-dropdown-variations .rid-cod-variations-external select.rid-cod-variation-select:focus {
    outline: none;
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(var(--rid-cod-accent-color-rgb), 0.1);
}

/* Variation sizes support for third form - same as classic form */
.rid-cod-form-third.rid-cod-variation-size-small .rid-cod-variations-external .variation-option {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 35px;
    min-height: 35px;
}

.rid-cod-form-third.rid-cod-variation-size-small .rid-cod-variations-external .variation-option.color-option {
    width: 28px;
    height: 28px;
}

.rid-cod-form-third.rid-cod-variation-size-medium .rid-cod-variations-external .variation-option {
    padding: 10px 16px;
    font-size: 14px;
    min-width: 45px;
    min-height: 45px;
}

.rid-cod-form-third.rid-cod-variation-size-medium .rid-cod-variations-external .variation-option.color-option {
    width: 35px;
    height: 35px;
}

.rid-cod-form-third.rid-cod-variation-size-large .rid-cod-variations-external .variation-option {
    padding: 14px 20px;
    font-size: 16px;
    min-width: 55px;
    min-height: 55px;
}

.rid-cod-form-third.rid-cod-variation-size-large .rid-cod-variations-external .variation-option.color-option {
    width: 45px;
    height: 45px;
}

.rid-cod-form-third.rid-cod-variation-size-extra-large .rid-cod-variations-external .variation-option {
    padding: 16px 24px;
    font-size: 18px;
    min-width: 65px;
    min-height: 65px;
}

.rid-cod-form-third.rid-cod-variation-size-extra-large .rid-cod-variations-external .variation-option.color-option {
    width: 55px;
    height: 55px;
}

/* Animation effects for third form variations - same as classic form */
.rid-cod-form-third .rid-cod-variations-external .variation-option {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option:hover {
    transform: translateY(-2px);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.selected {
    animation: selectPulse 0.3s ease-out;
}

/* Responsive design for third form variations */
@media (max-width: 768px) {
    .rid-cod-form-third .rid-cod-variations-external .variation-boxes {
        justify-content: center;
        gap: 6px;
    }

    .rid-cod-form-third .rid-cod-variations-external .variation-option {
        padding: 8px 12px;
        font-size: 13px;
        min-width: 40px;
    }

    .rid-cod-form-third .rid-cod-variations-external .variation-option.color-option {
        width: 30px;
        height: 30px;
    }
}

/* Third form summary variation details styling - Hidden */
.rid-cod-form-third #rid-cod-summary-variation-details-third {
    display: none !important;
}



/* Ensure color swatches are visible in third form - override any conflicting styles */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[style],
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style] {
    background-color: inherit !important;
}

/* Force color display for third form - ensure colors always show */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option {
    background: transparent !important;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch {
    background-color: var(--color, #e9ecef) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Override any hiding of color swatches in third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style] {
    display: block !important;
}

/* Ensure color swatches are always visible in third form regardless of settings */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch {
    background-color: inherit !important;
}

/* Additional CSS to force color display in third form */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color] .color-swatch {
    background-color: attr(data-color) !important;
}

/* Fallback using CSS custom properties */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option {
    --swatch-color: attr(data-color);
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch {
    background-color: var(--swatch-color, #e9ecef) !important;
}

/* Direct style attribute support - highest priority */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option .color-swatch[style*="background-color"] {
    /* This will use the inline style with !important */
    display: block !important;
}

/* Ultimate fallback - force all color swatches to be visible */
.rid-cod-form-third .rid-cod-variations-external .color-swatch {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 50% !important;
}

/* Ensure the parent container is positioned correctly */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option {
    position: relative !important;
}

/* Force color display using attribute selector */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ff0000"] .color-swatch { background-color: #ff0000 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ffa500"] .color-swatch { background-color: #ffa500 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#0000ff"] .color-swatch { background-color: #0000ff !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#000000"] .color-swatch { background-color: #000000 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ffffff"] .color-swatch { background-color: #ffffff !important; border: 2px solid #ddd !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#00ff00"] .color-swatch { background-color: #00ff00 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ffff00"] .color-swatch { background-color: #ffff00 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#800080"] .color-swatch { background-color: #800080 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#a52a2a"] .color-swatch { background-color: #a52a2a !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ffc0cb"] .color-swatch { background-color: #ffc0cb !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#808080"] .color-swatch { background-color: #808080 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#ffd700"] .color-swatch { background-color: #ffd700 !important; }
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option[data-color="#c0c0c0"] .color-swatch { background-color: #c0c0c0 !important; }

/* General styling for variation options */
.rid-cod-variations-external .variation-option {
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    min-width: 50px;
    text-align: center;
    display: inline-block;
    position: relative;
}

.rid-cod-variations-external .variation-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(var(--rid-cod-accent-color-rgb), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--rid-cod-accent-color-rgb), 0.2);
}

.rid-cod-variations-external .variation-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(var(--rid-cod-accent-color-rgb), 0.3);
}

/* Color options for external variations */
.rid-cod-variations-external .variation-option.color-option {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid #e2e8f0 !important;
}

/* Special handling for white colors */
.rid-cod-variations-external .variation-option.color-option[style*="background-color: #ffffff"],
.rid-cod-variations-external .variation-option.color-option[style*="background-color: white"] {
    border: 2px solid #d1d5db !important;
}

.rid-cod-variations-external .variation-option.color-option.selected {
    box-shadow: 0 0 0 3px var(--rid-cod-accent-color), 0 6px 15px rgba(var(--rid-cod-accent-color-rgb), 0.3);
}

.rid-cod-variations-external .variation-option.color-option .color-name {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 11px;
    color: #2d3748;
    white-space: nowrap;
}



/* Customer info layout for third form */
.rid-cod-form-third .rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

/* State and city fields for third form - full width */
.rid-cod-form-third .rid-cod-customer-info .rid-cod-field-group:nth-child(n+3) {
    grid-column: 1 / -1;
}

.rid-cod-form-third .rid-cod-field-group {
    position: relative;
}

.rid-cod-form-third .rid-cod-field-group input,
.rid-cod-form-third .rid-cod-field-group select,
.rid-cod-form-third .rid-cod-field-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    background: #ffffff;
    transition: all 0.2s ease;
    font-family: inherit;
}

.rid-cod-form-third .rid-cod-field-group input:focus,
.rid-cod-form-third .rid-cod-field-group select:focus,
.rid-cod-form-third .rid-cod-field-group textarea:focus {
    outline: none;
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(var(--rid-cod-accent-color-rgb), 0.1);
}

/* Third Form - Fix phone field RTL alignment */
.rid-cod-form-third .rid-cod-field-group input[type="tel"] {
    direction: rtl !important;
    text-align: right !important;
}

.rid-cod-form-third .rid-cod-field-group input[type="tel"]::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}



/* Delivery options for third form */
.rid-cod-form-third .rid-delivery-options {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.rid-cod-form-third .delivery-option {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    padding: 12px 16px !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 8px !important;
    background: #ffffff !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
}

.rid-cod-form-third .delivery-option:hover {
    border-color: var(--rid-cod-accent-color) !important;
    background: rgba(var(--rid-cod-accent-color-rgb), 0.05) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(var(--rid-cod-accent-color-rgb), 0.1) !important;
}

.rid-cod-form-third .delivery-option.selected {
    border-color: var(--rid-cod-accent-color) !important;
    background: rgba(var(--rid-cod-accent-color-rgb), 0.1) !important;
    color: var(--rid-cod-accent-color) !important;
    font-weight: 600 !important;
}

.rid-cod-form-third .delivery-option input[type="radio"] {
    width: 20px !important;
    height: 20px !important;
    accent-color: var(--rid-cod-accent-color) !important;
    cursor: pointer !important;
}

/* Custom Summary for Third Form - Enhanced with animations */
.rid-cod-form-third .rid-cod-summary-third {
    background: #f7fafc;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.rid-cod-form-third .rid-cod-summary-third:hover {
    box-shadow: 0 4px 12px rgba(197, 48, 48, 0.1);
}

.rid-cod-form-third .rid-cod-summary-third .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.rid-cod-form-third .rid-cod-summary-third .summary-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: var(--rid-cod-accent-color);
    font-size: 16px;
}

.rid-cod-form-third .rid-cod-summary-third .summary-row.total-row {
    border-bottom: none;
    font-weight: 600;
    color: var(--rid-cod-accent-color);
    font-size: 16px;
    background: rgba(var(--rid-cod-accent-color-rgb), 0.05);
    margin: 0 -20px;
    padding: 12px 20px;
    border-radius: 8px;
}

.rid-cod-form-third .rid-cod-summary-third .summary-label {
    color: #4a5568;
    font-weight: 500;
}

.rid-cod-form-third .rid-cod-summary-third .summary-value {
    color: #2d3748;
    font-weight: 600;
    transition: all 0.2s ease;
}

.rid-cod-form-third .rid-cod-summary-third .summary-value.updating {
    opacity: 0.6;
    transform: scale(0.95);
}

.rid-cod-form-third .rid-cod-summary-third .total-price {
    color: #16a085 !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(22, 160, 133, 0.2);
}

/* Free shipping indicator */
.rid-cod-form-third .rid-cod-summary-third .free-shipping {
    color: #16a085;
    font-weight: 600;
    background: rgba(22, 160, 133, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

/* Quantity controls for third form */
.rid-cod-form-third .quantity-controls-third {
    display: flex !important;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    position: relative;
    z-index: 10;
}

.rid-cod-form-third .quantity-btn {
    width: 45px !important;
    height: 45px !important;
    border: 3px solid var(--rid-cod-accent-color) !important;
    background: #ffffff !important;
    color: var(--rid-cod-accent-color) !important;
    border-radius: 50% !important;
    font-size: 20px !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    z-index: 9999 !important;
    pointer-events: auto !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    outline: none !important;
}

.rid-cod-form-third .quantity-btn:hover {
    background: var(--rid-cod-accent-color) !important;
    color: #ffffff !important;
    transform: scale(1.15) !important;
    box-shadow: 0 4px 15px rgba(var(--rid-cod-accent-color-rgb), 0.4) !important;
}

.rid-cod-form-third .quantity-btn:active {
    transform: scale(0.9) !important;
    background: rgba(var(--rid-cod-accent-color-rgb), 0.8) !important;
}

.rid-cod-form-third .quantity-display {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    min-width: 40px;
    text-align: center;
}

/* Submit button for third form */
.rid-cod-form-third .rid-cod-submit-btn {
    width: 100%;
    background: var(--rid-cod-accent-color);
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 18px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 20px;
    font-family: inherit;
}

.rid-cod-form-third .rid-cod-submit-btn:hover {
    background: rgba(var(--rid-cod-accent-color-rgb), 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(var(--rid-cod-accent-color-rgb), 0.3);
}

.rid-cod-form-third .rid-cod-submit-btn:active {
    transform: translateY(0);
}





/* Submit button for third form */
.rid-cod-form-third .rid-cod-submit-btn-third {
    width: 100%;
    background: var(--rid-cod-accent-color);
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 25px 20px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 25px;
    font-family: inherit;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rid-cod-form-third .rid-cod-submit-btn-third:hover {
    background: rgba(var(--rid-cod-accent-color-rgb), 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--rid-cod-accent-color-rgb), 0.4);
}

.rid-cod-form-third .rid-cod-submit-btn-third:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(var(--rid-cod-accent-color-rgb), 0.3);
}

.rid-cod-form-third .rid-cod-submit-btn-third:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Delivery section styling for third form */
.rid-cod-form-third .rid-cod-delivery-section {
    margin: 20px 0;
}

.rid-cod-form-third .delivery-label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 14px;
}

/* Location icon for delivery section */
.rid-cod-form-third .delivery-label::before {
    content: "📍";
    margin-left: 8px;
}

/* Summary section styling improvements */
.rid-cod-form-third .summary-row:last-child .summary-value {
    color: #16a085;
    font-size: 18px;
    font-weight: 700;
}

/* Hide icons for third form inputs */
.rid-cod-form-third .rid-input-icon {
    display: none;
}

/* Adjust input padding for third form */
.rid-cod-form-third .rid-cod-field-group input,
.rid-cod-form-third .rid-cod-field-group select {
    padding: 15px;
}

/* Responsive design for third form */
@media (max-width: 768px) {
    .rid-cod-form-third #rid-cod-checkout {
        margin: 0 15px;
        padding: 20px;
    }
    
    .rid-cod-form-third .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .rid-cod-form-third .rid-cod-customer-info .rid-cod-field-group:nth-child(n+3) {
        grid-column: 1;
    }
    
    .rid-cod-form-third .rid-delivery-options {
        flex-direction: column;
        gap: 10px;
    }
    
    .rid-cod-form-third .rid-cod-variations .variation-boxes {
        justify-content: center;
    }
    
    .rid-cod-form-third .quantity-controls {
        gap: 12px;
    }
    
    .rid-cod-form-third .quantity-btn {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

#rid-cod-form select {
    padding-right: 45px !important; /* Ensure space for icon and arrow */
    padding-left: 15px !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center; /* Arrow on the left for RTL */
    background-size: 16px 12px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

#rid-cod-form input:focus,
#rid-cod-form select:focus,
#rid-cod-form textarea:focus {
    border-color: #80bdff; /* Standard focus color */
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Standard focus shadow */
}

/* Input Icons */
.rid-cod-field-with-icon .rid-input-icon {
    position: absolute;
    top: 50%;
    right: 15px; /* Position icon on the right */
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.6;
}

.rid-icon-user { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6m2-3a2 2 0 1 1-4 0 2 2 0 0 1 4 0m4 8c0 1-1 1-1 1H3s-1 0-1-1 1-4 6-4 6 3 6 4m-1-.004c-.001-.246-.154-.986-.832-1.664C11.516 10.68 10.289 10 8 10s-3.516.68-4.168 1.332c-.678.678-.83 1.418-.832 1.664z"/></svg>'); }
.rid-icon-phone { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/></svg>'); }
.rid-icon-state { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path fill-rule="evenodd" d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6"/></svg>'); }
.rid-icon-city { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="%236c757d"><path d="M8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4.5a.5.5 0 0 0 .5-.5v-4h2v4a.5.5 0 0 0 .5.5H14a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354zM2.5 14V7.707l5.5-5.5 5.5 5.5V14H10v-4a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5v4z"/></svg>'); }
.rid-icon-cart { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-cart-fill" viewBox="0 0 16 16"><path d="M0 1.5A.5.5 0 0 1 .5 1H2a.5.5 0 0 1 .485.379L2.89 3H14.5a.5.5 0 0 1 .491.592l-1.5 8A.5.5 0 0 1 13 12H4a.5.5 0 0 1-.491-.408L2.01 3.607 1.61 2H.5a.5.5 0 0 1-.5-.5M5 12a2 2 0 1 0 0 4 2 2 0 0 0 0-4m7 0a2 2 0 1 0 0 4 2 2 0 0 0 0-4m-7 1a1 1 0 1 1 0 2 1 1 0 0 1 0-2m7 0a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/></svg>'); }
.rid-icon-arrow-down { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-chevron-down" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/></svg>'); }
.rid-icon-arrow-up { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="var(--rid-cod-accent-color)" class="bi bi-chevron-up" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708z"/></svg>'); }
.rid-icon-whatsapp { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-whatsapp" viewBox="0 0 16 16"><path d="M13.601 2.326A7.85 7.85 0 0 0 7.994 0C3.627 0 .068 3.558.064 7.926c0 1.399.366 2.76 1.057 3.965L0 16l4.204-1.102a7.9 7.9 0 0 0 3.79.965h.004c4.368 0 7.926-3.558 7.93-7.93A7.9 7.9 0 0 0 13.6 2.326zM7.994 14.521a6.6 6.6 0 0 1-3.356-.92l-.24-.144-2.494.654.666-2.433-.156-.251a6.56 6.56 0 0 1-1.007-3.505c0-3.626 2.957-6.584 6.591-6.584a6.56 6.56 0 0 1 4.66 1.931 6.56 6.56 0 0 1 1.928 4.66c-.004 3.639-2.961 6.592-6.592 6.592m3.615-4.934c-.197-.099-1.17-.578-1.353-.646-.182-.065-.315-.099-.445.099-.133.197-.513.646-.627.775-.114.133-.232.148-.43.05-.197-.1-.836-.308-1.592-.985-.59-.525-.985-1.175-1.103-1.372-.114-.198-.011-.304.088-.403.087-.088.197-.232.296-.346.1-.114.133-.198.198-.33.065-.134.034-.248-.015-.347-.05-.1-.445-1.076-.612-1.47-.16-.389-.323-.335-.445-.34-.114-.007-.247-.007-.38-.007a.73.73 0 0 0-.529.247c-.182.198-.691.677-.691 1.654s.71 1.916.81 2.049c.098.133 1.394 2.132 3.383 2.992.47.205.84.326 1.129.418.475.152.904.129 1.246.08.38-.058 1.171-.48 1.338-.943.164-.464.164-.86.114-.943-.049-.084-.182-.133-.38-.232"/></svg>'); }

/* Variations (Original - Kept for reference, but new styles below take precedence) */
/*
.rid-cod-variations {
    margin-bottom: 15px;
}
.rid-cod-variations select {
    margin-top: 5px;
}
*/

/* WooCommerce Compatible Variations Styles - Enhanced Design */
.rid-cod-variations {
    margin-bottom: 20px;
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.rid-cod-variations:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #dee2e6;
}

/* Ensure WooCommerce variation form is visible */
.rid-cod-variations .variations_form {
    display: block !important;
}

/* Style the variations table with modern design */
.rid-cod-variations .variations {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.rid-cod-variations .variations td {
    padding: 12px 0;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.rid-cod-variations .variations tr:last-child td {
    border-bottom: none;
}

.rid-cod-variations .variations td.label {
    width: 35%;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    padding-right: 15px;
}

.rid-cod-variations .variations td.value {
    width: 65%;
}

/* Box-style variations display */
.rid-cod-variations .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.rid-cod-variations .variation-option {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 85px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    color: #495057;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.rid-cod-variations .variation-option:hover {
    border-color: #c1c9d0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

.rid-cod-variations .variation-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: linear-gradient(135deg, #f0f7ff 0%, #e6f0fd 100%);
    color: var(--rid-cod-accent-color);
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(106, 61, 232, 0.15);
}

/* For color variations */
.rid-cod-variations .variation-option.color-option {
    min-width: 50px;
    min-height: 50px;
    padding: 5px;
    border-radius: 50%;
}

/* Error highlight for variation boxes */
.rid-cod-variations .variation-boxes.error-highlight {
    animation: shake 0.5s ease-in-out;
    border: 2px dashed var(--rid-cod-error-color);
    border-radius: 8px;
    padding: 8px;
    background-color: rgba(220, 50, 50, 0.05);
}

.rid-cod-variations .variation-boxes.error-highlight .variation-option {
    border-color: rgba(220, 50, 50, 0.3);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Disabled variation options */
.rid-cod-variations .variation-option.disabled,
.rid-cod-variations .variation-option.unavailable {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

/* Animation for becoming disabled/enabled */
.rid-cod-variations .variation-option.becoming-disabled {
    animation: fadeToDisabled 0.3s ease-out forwards;
}

.rid-cod-variations .variation-option.becoming-enabled {
    animation: fadeToEnabled 0.3s ease-out forwards;
}

@keyframes fadeToDisabled {
    from { opacity: 1; }
    to { opacity: 0.4; }
}

@keyframes fadeToEnabled {
    from { opacity: 0.4; }
    to { opacity: 1; }
}

/* For color variations - Original Design (Circles Only) */
.rid-cod-variations .variation-option.color-option {
    position: relative;
    width: 55px;
    height: 55px;
    min-width: 55px;
    padding: 0;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.rid-cod-variations .variation-option.color-option .color-swatch {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Hide color names in color options */
.rid-cod-variations .variation-option.color-option .color-name {
    display: none;
}

.rid-cod-variations .variation-option.color-option.selected {
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 2px var(--rid-cod-accent-color);
}

.rid-cod-variations .variation-option.color-option.selected:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.5));
}

/* Variation Images Styling */
.rid-cod-variations .variation-option.color-option .variation-image-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    overflow: hidden;
    z-index: 2;
}

/* Hide color swatch when image is present */
.rid-cod-variations .variation-option.color-option.has-image .color-swatch {
    display: none !important;
}

/* Ensure variation image is visible and properly sized */
.rid-cod-variations .variation-option.color-option.has-image {
    position: relative;
    overflow: hidden;
}

.rid-cod-variations .variation-option.color-option.has-image .variation-image-wrapper {
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 10 !important;
}

.rid-cod-variations .variation-option.color-option.has-image .variation-image {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: inherit !important;
}

/* Third form specific styling */
.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .color-swatch {
    display: none !important;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image-wrapper {
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 10 !important;
    border-radius: inherit !important;
}

.rid-cod-form-third .rid-cod-variations-external .variation-option.color-option.has-image .variation-image {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: inherit !important;
}

.rid-cod-variations .variation-option.color-option .variation-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: inherit;
    transition: transform 0.2s ease;
}

/* Hover effect for variation images */
.rid-cod-variations .variation-option.color-option:hover .variation-image {
    transform: scale(1.05);
}

/* Selected state for variation images */
.rid-cod-variations .variation-option.color-option.selected .variation-image {
    transform: scale(0.95);
}

/* Responsive sizing for variation images */
@media (max-width: 768px) {
    .rid-cod-variations .variation-option.color-option {
        width: 45px;
        height: 45px;
        min-width: 45px;
    }
}

@media (max-width: 480px) {
    .rid-cod-variations .variation-option.color-option {
        width: 40px;
        height: 40px;
        min-width: 40px;
    }
}

/* Square variant for variation images */
.rid-cod-variations.square-images .variation-option.color-option {
    border-radius: 8px;
}

.rid-cod-variations.square-images .variation-option.color-option .variation-image-wrapper,
.rid-cod-variations.square-images .variation-option.color-option .variation-image {
    border-radius: 8px;
}

/* Large size variant */
.rid-cod-variations.large-images .variation-option.color-option {
    width: 70px;
    height: 70px;
    min-width: 70px;
}

/* Extra large size variant */
.rid-cod-variations.extra-large-images .variation-option.color-option {
    width: 85px;
    height: 85px;
    min-width: 85px;
}

/* Small size variant */
.rid-cod-variations.small-images .variation-option.color-option {
    width: 40px;
    height: 40px;
    min-width: 40px;
}

/* Loading state for images */
.rid-cod-variations .variation-option.color-option .variation-image {
    background-color: #f8f9fa;
    background-image: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                      linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                      linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
}

.rid-cod-variations .variation-option.color-option .variation-image[src] {
    background: none;
}

/* Fallback for when image fails to load */
.rid-cod-variations .variation-option.color-option .variation-image:not([src]),
.rid-cod-variations .variation-option.color-option .variation-image[src=""] {
    display: none;
}

/* Image selection animation */
.rid-cod-variations .variation-option.color-option.image-selecting {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* Product image update animation */
.woocommerce-product-gallery__image img.image-updating,
.wp-post-image.image-updating {
    opacity: 0.7;
    transition: opacity 0.15s ease;
}

/* Fallback styling when no image is available */
.rid-cod-variations .variation-option.color-option:not(.has-image) .color-swatch {
    border: 2px solid rgba(0,0,0,0.1);
}

.rid-cod-variations .variation-option.color-option:not(.has-image).selected .color-swatch {
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 1px var(--rid-cod-accent-color);
}

/* Enhanced styling for color names when used as fallback */
.rid-cod-variations .variation-option.color-option:not(.has-image) .color-name {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    text-align: center;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rid-cod-variations .variation-option.color-option:not(.has-image).selected .color-name {
    background: linear-gradient(135deg, var(--rid-cod-accent-color) 0%, rgba(var(--rid-cod-accent-color-rgb), 0.8) 100%);
    color: white;
    border-color: var(--rid-cod-accent-color);
}

/* Hide original select elements but keep them in DOM for form submission */
.rid-cod-variations .variations select {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
    pointer-events: none;
}

/* Show select elements when dropdown mode is enabled */
.rid-cod-use-dropdown-variations .rid-cod-variations .variations select,
.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
    position: static;
    opacity: 1;
    height: auto;
    width: 100%;
    pointer-events: auto;
    display: block;
    margin-bottom: 15px;
}

/* Hide variation boxes when dropdown mode is enabled */
.rid-cod-use-dropdown-variations .rid-cod-variations .variation-boxes {
    display: none !important;
}

/* Enhanced styling for dropdown variations mode */
.rid-cod-use-dropdown-variations .rid-cod-variations .variation-row {
    margin-bottom: 20px;
}

.rid-cod-use-dropdown-variations .rid-cod-variations .variation-label {
    margin-bottom: 8px;
}

.rid-cod-use-dropdown-variations .rid-cod-variations .attribute-label {
    font-weight: 600;
    color: #495057;
    display: block;
    margin-bottom: 5px;
}

/* Styling for disabled options in dropdown mode */
.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select option:disabled {
    color: #999 !important;
    background-color: #f5f5f5 !important;
    font-style: italic;
}

.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select.has-disabled-options {
    border-color: #ffc107;
    background-color: #fffbf0;
}

.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select.has-disabled-options:focus {
    border-color: var(--rid-cod-accent-color);
    background-color: #ffffff;
}

/* Attribute label styling */
.rid-cod-variations .attribute-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    margin-top: 15px;
}

/* Modern dropdown styling matching the plugin design */
.rid-cod-variations .variations select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    font-size: 14px;
    color: #495057;
    font-family: inherit;
    transition: all 0.3s ease;
    cursor: pointer;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="%23495057" viewBox="0 0 16 16"><path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    padding-left: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* RTL support for dropdown arrow */
.rtl .rid-cod-variations .variations select,
.rtl.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
    background-position: right 12px center;
    padding-right: 40px;
    padding-left: 16px;
}

/* RTL support for modern form dropdown variations */
.rtl.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
    background-position: right 12px center;
    padding-right: 40px;
    padding-left: 16px;
}

/* RTL support for labels */
.rtl .rid-cod-variations .variations td.label {
    padding-left: 15px;
    padding-right: 0;
    text-align: right;
}

/* Order Summary Table Styles */
.rid-cod-order-summary table,
.rid-cod-order-summary tr,
.rid-cod-order-summary td {
    border: none !important;
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    box-shadow: none !important;
    background: transparent !important;
}

.rid-cod-order-summary table {
    margin: 0 !important;
    width: 100% !important;
}

.rid-cod-order-summary td {
    padding: 10px !important;
    vertical-align: middle !important;
}

.rid-cod-order-summary tr:last-child {
    font-weight: bold;
}

.rid-cod-order-summary tr:last-child td {
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    padding-top: 15px !important;
}

/* Accessibility improvements */
.rid-cod-variations .variations select:focus-visible {
    outline: 2px solid var(--rid-cod-accent-color);
    outline-offset: 2px;
}

.rid-cod-variations .variations select[aria-invalid="true"] {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15);
}

/* Loading state for variations */
.rid-cod-variations.loading {
    opacity: 0.7;
    pointer-events: none;
}

.rid-cod-variations.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--rid-cod-accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.rid-cod-variations .variations select:focus {
    border-color: var(--rid-cod-accent-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
    transform: translateY(-1px);
}

.rid-cod-variations .variations select:hover {
    border-color: #ced4da;
    background-color: #f8f9fa;
}

/* Style the single variation display */
.rid-cod-variations .single_variation_wrap {
    margin-top: 15px;
    padding: 0;
    background: transparent;
    border: none;
}

.rid-cod-variations .single_variation_wrap .woocommerce-variation {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 13px;
    color: #6c757d;
}

/* Reset variations link */
.rid-cod-variations .reset_variations {
    font-size: 12px;
    color: #6c757d;
    text-decoration: none;
    margin-top: 8px;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.rid-cod-variations .reset_variations:hover {
    color: var(--rid-cod-accent-color);
    background-color: rgba(106, 61, 232, 0.1);
    text-decoration: none;
}

/* Reset variations link for third form - same styling */
.rid-cod-form-third .rid-cod-variations-external .reset_variations {
    font-size: 12px;
    color: #6c757d;
    text-decoration: none;
    margin-top: 8px;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.rid-cod-form-third .rid-cod-variations-external .reset_variations:hover {
    color: var(--rid-cod-accent-color);
    background-color: rgba(var(--rid-cod-accent-color-rgb), 0.1);
    text-decoration: none;
}

/* Responsive Design for Variations */
@media (max-width: 768px) {
    .rid-cod-variations {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 10px;
    }

    .rid-cod-variations .variations td.label {
        width: 100%;
        padding-right: 0;
        padding-bottom: 6px;
        font-size: 13px;
    }

    .rid-cod-variations .variations td.value {
        width: 100%;
        padding-top: 0;
    }

    .rid-cod-variations .variations td {
        display: block;
        padding: 6px 0;
    }

    .rid-cod-variations .variations select,
    .rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
        padding: 10px 14px;
        padding-left: 36px;
        font-size: 13px;
    }

    .rtl .rid-cod-variations .variations select,
    .rtl.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
        padding-right: 36px;
        padding-left: 14px;
    }

    /* Mobile styling for dropdown variations */
    .rid-cod-use-dropdown-variations .rid-cod-variations .variation-row {
        margin-bottom: 16px;
    }

    .rid-cod-use-dropdown-variations .rid-cod-variations .attribute-label {
        font-size: 13px;
        margin-bottom: 6px;
    }
}

/* Enhanced variation section title */
.rid-cod-variations::before {
    content: "🎨 " attr(data-title);
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f1f3f4;
}

/* Add subtle animation on variation change */
.rid-cod-variations .variations select.changing {
    transform: scale(1.02);
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
}

/* Animation when variation is updated */
.rid-cod-variations.variation-updated {
    transform: scale(1.01);
    box-shadow: 0 6px 20px rgba(106, 61, 232, 0.15);
    border-color: var(--rid-cod-accent-color);
}

/* Smooth transitions for all variation elements */
.rid-cod-variations,
.rid-cod-variations .variations select,
.rid-cod-variations .single_variation_wrap {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}


/* Delivery Type Section - Main Form */
.rid-cod-delivery-section {
    margin-bottom: 20px;
    background: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--rid-cod-border-color);
}

.rid-cod-delivery-section .delivery-label {
    display: block;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--rid-cod-label-color);
    font-size: 14px;
    text-align: center;
}

.rid-delivery-options {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.rid-delivery-options .delivery-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--rid-cod-text-color);
    padding: 8px 12px;
    border: 1px solid var(--rid-cod-border-color);
    border-radius: 6px;
    background: var(--rid-cod-primary-bg-color);
    transition: all 0.3s ease;
    position: relative;
    min-width: 120px;
    justify-content: flex-start;
    gap: 8px;
}

.rid-delivery-options .delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(177, 156, 217, 0.05);
}

.rid-delivery-options .delivery-option input[type="radio"] {
    display: none;
}

.rid-delivery-options .radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 50%;
    position: relative;
    background: white;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.rid-delivery-options .delivery-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
}

.rid-delivery-options .delivery-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

.rid-delivery-options .delivery-option input[type="radio"]:checked ~ .delivery-text {
    color: var(--rid-cod-accent-color);
    font-weight: 600;
}

.rid-delivery-options .delivery-text {
    font-weight: 500;
    transition: color 0.2s ease;
}

/* Conditional Order Summary Visibility */
/* When delivery type is in main form, optionally hide summary */
.rid-cod-form-container.delivery-in-main-form.hide-summary #rid-cod-summary-wrapper {
    display: none;
}

/* Enhanced styling for selected delivery options */
.rid-delivery-options .delivery-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: rgba(177, 156, 217, 0.1);
    box-shadow: 0 2px 8px rgba(177, 156, 217, 0.15);
}

/* Classic Form - Delivery type in summary */
.summary-delivery-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.summary-delivery-option {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    width: 100%;
}

.summary-delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(177, 156, 217, 0.1);
}

.summary-delivery-option input[type="radio"] {
    display: none;
}

.summary-delivery-option .summary-radio-custom {
    width: 14px;
    height: 14px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.summary-delivery-option input[type="radio"]:checked + .summary-radio-custom {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
}

.summary-delivery-option input[type="radio"]:checked + .summary-radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
}

.summary-delivery-option .summary-delivery-text {
    font-weight: 500;
    color: #333;
}

.summary-delivery-options .summary-delivery-option.selected {
    background: rgba(177, 156, 217, 0.1);
    border-color: var(--rid-cod-accent-color);
}


/* Actions Row (Submit Button and Quantity) */
.rid-cod-actions-row {
    display: grid;
    grid-template-columns: 1fr auto; /* Submit button takes remaining space */
    grid-gap: 15px;
    align-items: center;
    margin-top: 10px; /* Space above actions */
}

/* Quantity selector styles */
.rid-cod-quantity {
    display: flex; /* Use flex for internal alignment */
    align-items: center;
    justify-content: center; /* Center items horizontally */
}

.rid-cod-quantity-selector {
    display: flex; /* Use flex for button/input alignment */
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-radius: 6px;
    overflow: hidden;
    height: 48px; /* Match input height */
}

#rid-cod-decrease,
#rid-cod-increase {
    background: #f8f9fa; /* Lighter background */
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--rid-cod-accent-color); /* Use CSS Variable */
    width: 40px; /* Fixed width */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

#rid-cod-decrease:hover,
#rid-cod-increase:hover {
    background: #e9ecef; /* Slightly darker on hover */
}

#rid-cod-quantity-input {
    text-align: center;
    border: none;
    border-left: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    border-right: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    padding: 5px;
    font-size: 16px; /* Slightly smaller */
    font-weight: 600;
    width: 50px; /* Fixed width */
    height: 100%; /* Fill height */
    -moz-appearance: textfield; /* Hide arrows for number input */
    appearance: textfield; /* Standard property for compatibility */
}
#rid-cod-quantity-input::-webkit-outer-spin-button,
#rid-cod-quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Submit button styles */
.rid-cod-submit {
    display: flex; /* Use flex to position the button */
    justify-content: stretch; /* Stretch button to fill space */
    align-items: center; /* Center button vertically */
}

#rid-cod-submit-btn {
    width: 100%;
    padding: 0 15px; /* Adjust padding */
    height: 48px; /* Match input height */
    background-color: var(--rid-cod-button-bg-color); /* Use CSS Variable */
    color: var(--rid-cod-button-text-color); /* Use CSS Variable */
    border: none;
    border-radius: 6px;
    font-size: 16px; /* Slightly smaller */
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.1s ease;
    box-shadow: 0 4px 10px rgba(106, 61, 232, 0.3);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase; /* Uppercase text like image */
}

#rid-cod-submit-btn:hover {
    /* Consider adding a slightly darker version of the button bg color variable */
    background-color: #5a30d8; /* Darker purple on hover - TODO: Make dynamic? */
    box-shadow: 0 6px 15px rgba(106, 61, 232, 0.4);
    transform: translateY(-2px);
}

#rid-cod-submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(106, 61, 232, 0.3);
}

/* WhatsApp Button Styles */
.rid-cod-whatsapp-button {
    margin-top: 15px; /* Space above WhatsApp button */
}

#rid-cod-whatsapp-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 15px;
    height: 48px;
    background-color: #25d366; /* WhatsApp green */
    color: #fff;
    border: none;
    border-radius: 24px; /* Pill shape */
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 10px rgba(37, 211, 102, 0.3);
    text-decoration: none;
}

#rid-cod-whatsapp-btn .rid-icon-whatsapp {
    width: 20px; /* Larger icon */
    height: 20px;
    margin-left: 8px; /* Space between icon and text */
    display: inline-block;
    vertical-align: middle;
    filter: brightness(0) invert(1); /* Make SVG white */
}

#rid-cod-whatsapp-btn:hover {
    background-color: #1ebe5b; /* Darker green on hover */
    box-shadow: 0 6px 15px rgba(37, 211, 102, 0.4);
}

/* Order summary styles */
#rid-cod-summary-wrapper {
    margin-top: 25px;
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
    border-radius: 8px;
    border: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* Subtle shadow */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

#rid-cod-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px; /* Slightly less padding */
    cursor: pointer;
    border-bottom: 1px solid var(--rid-cod-border-color); /* Use CSS Variable */
    background-color: #f8f9fa; /* Light grey header background */
    transition: background-color 0.2s ease;
}
#rid-cod-summary-header:hover {
    background-color: #e9ecef; /* Darken header on hover */
}
/* Keep border when open/closed, managed by header style */
/* #rid-cod-summary-header.open { } */
/* #rid-cod-summary-header:not(.open) { border-bottom: none; } */


#rid-cod-summary-header h4 {
    font-size: 15px; /* Slightly smaller */
    margin-bottom: 0;
    color: var(--rid-cod-label-color); /* Use CSS Variable */
    font-weight: 700;
    display: flex;
    align-items: center;
}

#rid-cod-summary-header h4 .rid-icon-cart {
    width: 18px;
    height: 18px;
    margin-left: 8px; /* Space after icon */
}

#rid-cod-summary-toggle {
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transition: transform 0.3s ease;
}
#rid-cod-summary-toggle.open {
    transform: rotate(180deg);
}


#rid-cod-summary-content {
    padding: 15px;
    /* display: none; */ /* Controlled by JS */
    background-color: var(--rid-cod-primary-bg-color); /* Use CSS Variable */
}

#rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

#rid-cod-summary-content td {
    padding: 10px 0; /* Increased vertical padding */
    border-bottom: 1px solid #e9ecef; /* Lighter solid separator */
    font-size: 14px;
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    vertical-align: top;
    word-wrap: break-word;
}

#rid-cod-summary-content td:first-child {
    max-width: 65%;
    overflow-wrap: break-word;
    word-break: break-word;
}

#rid-cod-summary-content td:last-child {
    text-align: left;
    min-width: 35%;
}

#rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    padding-top: 15px; /* More space before total */
}

#rid-cod-summary-content tr.rid-cod-total td {
    font-weight: 700; /* Bolder total */
    color: var(--rid-cod-accent-color); /* Use CSS Variable */
    font-size: 17px; /* Larger total */
}

#rid-cod-summary-content tr td:first-child {
    text-align: right;
}

#rid-cod-summary-content tr td:last-child {
    text-align: left;
    font-weight: 500; /* Normal weight for prices */
}

/* Product name styling in summary */
.product-name {
    display: block;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 3px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Variation details in summary */
.rid-summary-variation-details {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.rid-summary-variation-details .variation-detail,
.rid-summary-variation-details .rid-variation-meta {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 3px;
    padding: 2px 6px;
    background-color: #f8f9fa;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive handling for long product names and variation details */
@media (max-width: 768px) {
    #rid-cod-summary-content {
        padding: 12px;
    }
    
    #rid-cod-summary-content td {
        font-size: 13px;
        padding: 8px 0;
    }
    
    #rid-cod-summary-content td:first-child {
        max-width: 60%;
        word-wrap: break-word;
        white-space: normal;
        line-height: 1.3;
    }
    
    #rid-cod-summary-content td:last-child {
        min-width: 40%;
        text-align: left;
    }
    
    .product-name {
        font-size: 13px;
        line-height: 1.2;
    }
    
    .rid-summary-variation-details .variation-detail,
    .rid-summary-variation-details .rid-variation-meta {
        display: block;
        white-space: normal;
        max-width: 100%;
        margin-bottom: 2px;
        margin-right: 0;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    #rid-cod-summary-content {
        padding: 10px;
    }
    
    #rid-cod-summary-content td {
        font-size: 12px;
        padding: 6px 0;
    }
    
    #rid-cod-summary-content td:first-child {
        max-width: 55%;
    }
    
    #rid-cod-summary-content td:last-child {
        min-width: 45%;
        font-size: 11px;
    }
    
    .product-name {
        font-size: 12px;
        line-height: 1.1;
        margin-bottom: 4px;
    }
    
    .rid-summary-variation-details {
        font-size: 10px;
        margin-top: 3px;
    }
    
    .rid-summary-variation-details .variation-detail,
    .rid-summary-variation-details .rid-variation-meta {
        font-size: 9px;
        padding: 1px 4px;
        margin-bottom: 1px;
    }

}

.rid-summary-quantity {
    display: inline-block;
    background-color: #e9ecef; /* Light grey background */
    color: var(--rid-cod-text-color); /* Use CSS Variable */
    font-size: 12px; /* Slightly larger */
    padding: 3px 8px; /* More padding */
    border-radius: 4px;
    margin-left: 8px; /* More space */
    font-weight: normal;
    vertical-align: middle;
}

/* Message styles */
#rid-cod-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 6px;
    display: none;
    font-size: 14px;
}

#rid-cod-message.success {
    background-color: #d4edda;
    color: var(--rid-cod-success-color); /* Use CSS Variable */
    border: 1px solid #c3e6cb;
    display: block;
}

#rid-cod-message.error {
    background-color: #f8d7da;
    color: var(--rid-cod-error-color); /* Use CSS Variable */
    border: 1px solid #f5c6cb;
    display: block;
}

/* Global message styling */
.rid-global-message {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    z-index: 10000 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    font-weight: 500 !important;
    max-width: 350px !important;
    word-wrap: break-word !important;
    direction: rtl !important;
    text-align: right !important;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif !important;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .rid-global-message {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        font-size: 13px !important;
        padding: 10px 15px !important;
    }
}

/* Success Popup Styles */
.rid-cod-popup {
    position: fixed; /* Position relative to the viewport */
    top: 20px; /* Position from the top */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Adjust horizontal centering */
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 99999; /* Ensure it's on top of other elements */
    display: none; /* Hidden by default, shown via JS */
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    max-width: 90%; /* Prevent it from being too wide on small screens */
    box-sizing: border-box;
}

.rid-cod-popup.success {
    background-color: #d4edda; /* Light green background */
    color: var(--rid-cod-success-color); /* Use CSS Variable */
    border: 1px solid #c3e6cb;
}

/* Optional: Add a subtle transition for fade-in/out if not handled solely by JS */
.rid-cod-popup {
    transition: opacity 0.5s ease-in-out;
}

/* Responsive styles */
@media (max-width: 768px) {
    #rid-cod-checkout {
        padding: 20px 15px;
    }
    .rid-cod-customer-info {
        grid-template-columns: 1fr; /* Stack fields on mobile */
    }
    .rid-cod-actions-row {
        grid-template-columns: 1fr; /* Stack actions on mobile */
        grid-gap: 10px;
    }
    .rid-cod-submit {
        order: 2; /* Place submit button below quantity */
    }
    .rid-cod-quantity {
        order: 1;
        justify-content: center; /* Center quantity selector */
    }
    #rid-cod-submit-btn,
    #rid-cod-whatsapp-btn {
        font-size: 15px;
        height: 44px;
    }
    #rid-cod-summary-header h4 {
        font-size: 15px;
    }
    #rid-cod-summary-content td {
        font-size: 13px;
    }

    /* Classic Form - Delivery options responsive */
    .rid-delivery-options {
        flex-direction: column;
        gap: 10px;
    }

    .rid-delivery-options .delivery-option {
        min-width: auto;
        width: 100%;
        justify-content: flex-start;
        padding: 12px;
    }

    /* Classic Form - Summary delivery options responsive */
    .summary-delivery-options {
        flex-direction: column;
        gap: 8px;
    }

    .summary-delivery-option {
        width: 100%;
        justify-content: flex-start;
        padding: 10px;
    }

    /* Fix summary table on mobile for classic form */
    #rid-cod-summary-wrapper {
        margin: 15px 0;
        border-radius: 10px;
        overflow: hidden;
    }

    /* Mobile-specific adjustments - keep borders lighter but visible */
    #rid-cod-summary-content td {
        padding: 10px 5px;
        font-size: 13px;
        word-wrap: break-word;
        vertical-align: top;
        /* Keep the original border but make it lighter on mobile */
        border-bottom: 1px solid #f5f5f5;
    }

    #rid-cod-summary-content tr:last-child td {
        border-bottom: none;
        font-weight: 700;
        color: var(--rid-cod-accent-color);
        padding-top: 12px;
    }

    #rid-cod-summary-content td:first-child {
        width: 60%;
        padding-right: 8px;
    }

    #rid-cod-summary-content td:last-child {
        width: 40%;
        text-align: left;
        padding-left: 8px;
    }

    /* Improve text wrapping for classic form */
    .product-name {
        font-size: 13px;
        line-height: 1.3;
        word-break: break-word;
    }

    .rid-summary-variation-details {
        font-size: 11px;
        margin-top: 4px;
        line-height: 1.2;
    }
}

/* --- Sticky Buy Button --- */
.rid-cod-sticky-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--rid-cod-primary-bg-color, #fff);
    padding: 20px 25px;
    box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(100%); /* Initially hidden */
    transition: transform 0.3s ease-in-out;
    border-top: 1px solid var(--rid-cod-border-color, #e0e0e0);
    direction: rtl;
    /* Centering properties */
    display: flex;
    justify-content: center;
    align-items: center;
}

.rid-cod-sticky-button-container.visible {
    transform: translateY(0);
    display: flex; /* Keep flex properties when visible */
}

#rid-cod-sticky-button-container #rid-cod-sticky-submit-btn {
    background-color: var(--rid-cod-sticky-button-bg-color);
    color: var(--rid-cod-sticky-button-text-color);
    border: none;
    border-radius: 8px;
    padding: 16px 40px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 61, 232, 0.3);
    text-transform: uppercase;
    min-width: 280px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

/* Override hover effect if needed, or let it inherit */
/* .rid-cod-sticky-button-container #rid-cod-submit-btn:hover {
    opacity: 0.9;
} */

/* Responsive adjustments for sticky button */
@media (max-width: 768px) {
    .rid-cod-sticky-button-container {
        padding: 10px 15px;
    }
    /* Button inherits responsive styles from base rule */

    /* External variations responsive */
    .rid-cod-variations-external {
        margin-bottom: 20px;
        padding: 20px 15px;
        max-width: 100%;
    }

    .rid-cod-variations-external .variation-option {
        padding: 10px 14px;
        font-size: 13px;
        min-width: 40px;
    }

    .rid-cod-variations-external .variation-option.color-option {
        width: 40px;
        height: 40px;
    }
}

/* RTL fixes if needed */
.rtl #rid-cod-form select {
    background-position: left 0.75rem center;
    padding-right: 45px !important;
    padding-left: 15px !important;
}
.rtl .rid-cod-field-with-icon .rid-input-icon {
    right: 15px;
    left: auto;
}
.rtl #rid-cod-form input[type="text"],
.rtl #rid-cod-form input[type="tel"],
.rtl #rid-cod-form input[type="email"],
.rtl #rid-cod-form select,
.rtl #rid-cod-form textarea {
    padding: 12px 40px 12px 15px; /* R L T B */
}

/* Fix phone field placeholder alignment for RTL */
#rid-cod-form input[type="tel"] {
    direction: rtl !important;
    text-align: right !important;
}

#rid-cod-form input[type="tel"]::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Ensure phone field works properly in all form styles */
.rid-cod-form-modern input[type="tel"],
.rid-cod-form-second input[type="tel"],
.rid-cod-form-third input[type="tel"] {
    direction: rtl !important;
    text-align: right !important;
}

.rid-cod-form-modern input[type="tel"]::placeholder,
.rid-cod-form-second input[type="tel"]::placeholder,
.rid-cod-form-third input[type="tel"]::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Additional fixes for phone field in different browsers */
input[type="tel"]#rid-cod-phone {
    direction: rtl !important;
    text-align: right !important;
    -webkit-text-direction: rtl !important;
    -moz-text-direction: rtl !important;
}

input[type="tel"]#rid-cod-phone::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
    -webkit-text-direction: rtl !important;
    -moz-text-direction: rtl !important;
}

/* Fix for WebKit browsers (Chrome, Safari) */
input[type="tel"]#rid-cod-phone::-webkit-input-placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Fix for Mozilla Firefox */
input[type="tel"]#rid-cod-phone::-moz-placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Fix for Microsoft Edge */
input[type="tel"]#rid-cod-phone::-ms-input-placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}
.rtl #rid-cod-whatsapp-btn .rid-icon-whatsapp {
    margin-left: 8px;
    margin-right: 0;
}
.rtl #rid-cod-summary-header h4 .rid-icon-cart {
     margin-left: 8px;
     margin-right: 0;
}
.rtl #rid-cod-summary-content tr td:first-child {
    text-align: right;
}
.rtl #rid-cod-summary-content tr td:last-child {
    text-align: left;
}

/* Variation Swatch Styles */
.rid-cod-variation-row {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;
}
.rid-cod-variation-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rid-cod-variation-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.rid-cod-swatches {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.rid-cod-swatch {
    display: inline-flex; /* Use inline-flex for alignment */
    align-items: center;
    justify-content: center;
    min-width: 40px; /* Minimum width */
    height: 40px; /* Fixed height */
    padding: 5px 15px; /* Padding for text */
    border: 2px solid #ced4da; /* Default border */
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    background-color: #fff;
    text-align: center;
    line-height: 1.5; /* Adjust line height */
}

.rid-cod-swatch:hover {
    border-color: #adb5bd; /* Darker border on hover */
}

.rid-cod-swatch.selected {
    border-color: #6a3de8; /* Purple border when selected */
    background-color: #f3efff; /* Light purple background */
    color: #6a3de8; /* Purple text */
    font-weight: 700;
    box-shadow: 0 0 0 2px rgba(106, 61, 232, 0.2); /* Subtle glow */
}

/* Color Swatches */
.rid-cod-swatch.rid-color-swatch {
    width: 40px; /* Fixed width for color swatches */
    min-width: 40px;
    padding: 0; /* Remove padding */
    overflow: hidden; /* Hide text if any */
    text-indent: -9999px; /* Hide text */
    position: relative; /* For pseudo-elements */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* Subtle shadow for all color swatches */
}

/* Special handling for white/light colors */
.rid-cod-swatch.rid-color-swatch.rid-swatch-white,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fff"],
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffffff"],
.rid-cod-swatch.rid-color-swatch[style*="background-color: rgb(255, 255, 255)"] {
    box-shadow: inset 0 0 0 1px #ddd; /* Inner border for better visibility */
}

/* Enhance visibility of selected light colors */
.rid-cod-swatch.rid-color-swatch.selected {
    box-shadow: 0 0 0 2px rgba(106, 61, 232, 0.7), inset 0 0 0 1px rgba(0,0,0,0.1); /* Stronger accent color glow */
}

/* Checkmark for selected colors */
.rid-cod-swatch.rid-color-swatch.selected:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    opacity: 0.9;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.5)); /* Make checkmark visible on any background */
}

/* Invert checkmark for light colors */
.rid-cod-swatch.rid-color-swatch.rid-swatch-white.selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffffff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffff"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: rgb(255, 255, 255)"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffd"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffc"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffa"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ff9"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ff8"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #eee"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f5f5"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f0e6"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fffffe0"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #fffff0"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #f5f5dc"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffdab9"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #ffd580"].selected:after,
.rid-cod-swatch.rid-color-swatch[style*="background-color: #d3d3d3"].selected:after {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23000000'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E");
    opacity: 0.7;
}

/* Add hover effect for color swatches */
.rid-cod-swatch.rid-color-swatch:hover {
    transform: scale(1.08);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}

/* Improved size swatches (text-based) */
.rid-cod-swatch.rid-size-swatch {
    /* Uses default swatch styles */
    min-width: 30px; /* Slightly smaller minimum width */
    padding: 5px 10px; /* Adjusted padding */
}

/* Disabled Swatches (Enhanced for linked variations) */
.rid-cod-swatch.disabled,
.rid-cod-variations .variation-option.disabled {
    opacity: 0.4;
    cursor: not-allowed !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    pointer-events: none;
    position: relative;
}

.rid-cod-swatch.disabled:hover,
.rid-cod-variations .variation-option.disabled:hover {
    border-color: #dee2e6 !important; /* Prevent hover effect */
    transform: none !important;
    box-shadow: none !important;
}

/* Enhanced message styling for cleared selections */
.rid-selection-cleared-message {
    position: absolute !important;
    top: -35px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(220, 53, 69, 0.95) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    font-size: 11px !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
}

/* Enhanced disabled styling for better visual feedback */
.rid-cod-variations .variation-option.disabled,
.rid-cod-swatch.disabled {
    opacity: 0.3 !important;
    cursor: not-allowed !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    pointer-events: none !important;
    position: relative;
    filter: grayscale(70%);
}

/* Improved strikethrough effect for unavailable options */
.rid-cod-variations .variation-option.unavailable::after,
.rid-cod-swatch.unavailable::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 8%;
    right: 8%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #dc3545 20%, #dc3545 80%, transparent);
    transform: translateY(-50%) rotate(-15deg);
    z-index: 2;
    box-shadow: 0 0 3px rgba(220, 53, 69, 0.5);
}

/* Enhanced disabled color swatch styling */
.rid-cod-swatch.rid-color-swatch.disabled {
    opacity: 0.25 !important;
    filter: grayscale(100%) brightness(1.2);
}

.rid-cod-swatch.rid-color-swatch.disabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.9) 2px,
        rgba(255,255,255,0.9) 4px,
        transparent 4px,
        transparent 6px,
        rgba(220, 53, 69, 0.7) 6px,
        rgba(220, 53, 69, 0.7) 8px
    );
    z-index: 2;
    border-radius: inherit;
}

/* Removed first attribute highlighting styles */

/* Selection cleared message */
.rid-selection-cleared-message {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(220, 53, 69, 0.95);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.2);
    pointer-events: none;
}

/* Global selection message */
.rid-global-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(23, 162, 184, 0.95);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: 1px solid rgba(255,255,255,0.2);
    max-width: 300px;
    word-wrap: break-word;
}

/* Additional Custom Styles */
/* Add any further custom styles below */

/* ========================================
   FORM STYLES - MODERN AND MOBILE VARIANTS
   ======================================== */

/* Modern Form Style - Updated to match the provided image exactly */
.rid-cod-form-modern #rid-cod-checkout {
    background: var(--rid-cod-primary-bg-color);
    border: 2px solid var(--rid-cod-accent-color);
    border-radius: 12px;
    color: var(--rid-cod-text-color);
    box-shadow: 0 4px 15px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.15);
    border-top: 2px solid var(--rid-cod-accent-color);
    padding: 25px;
    max-width: 600px;
    margin: 20px auto;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
}

.rid-cod-form-modern .rid-cod-title h3 {
    color: var(--rid-cod-text-color);
    font-size: 16px;
    margin-bottom: 25px;
    text-align: center;
    font-weight: 500;
    text-shadow: none;
    line-height: 1.4;
}

.rid-cod-form-modern .rid-cod-customer-info {
    background: transparent;
    padding: 0;
    border-radius: 0;
    margin-bottom: 25px;
    backdrop-filter: none;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.rid-cod-form-modern .rid-cod-field-group {
    margin-bottom: 15px;
    position: relative;
}

.rid-cod-form-modern .rid-cod-field-group input,
.rid-cod-form-modern .rid-cod-field-group select,
.rid-cod-form-modern .rid-cod-field-group textarea {
    border: 1px solid var(--rid-cod-border-color);
    background: var(--rid-cod-primary-bg-color);
    padding: 12px 15px;
    font-size: 14px;
    color: var(--rid-cod-text-color);
    border-radius: 8px;
    transition: border-color 0.3s ease;
    width: 100%;
    height: 48px;
}

.rid-cod-form-modern .rid-cod-field-group input:focus,
.rid-cod-form-modern .rid-cod-field-group select:focus,
.rid-cod-form-modern .rid-cod-field-group textarea:focus {
    border-color: var(--rid-cod-accent-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.2);
}

.rid-cod-form-modern .rid-cod-actions-row {
    background: transparent;
    padding: 0;
    border-radius: 0;
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 15px;
    backdrop-filter: none;
    margin-top: 20px;
}

.rid-cod-form-modern #rid-cod-submit-btn {
    background: var(--rid-cod-button-bg-color);
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    color: var(--rid-cod-button-text-color);
    font-weight: 600;
    font-size: 16px;
    box-shadow: none;
    transition: all 0.3s ease;
    flex: 1;
    height: 48px;
    text-transform: uppercase;
}

.rid-cod-form-modern #rid-cod-submit-btn:hover {
    background: var(--rid-cod-accent-color);
    transform: none;
    box-shadow: 0 2px 8px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.3);
}

.rid-cod-form-modern .rid-cod-quantity-selector {
    background: var(--rid-cod-primary-bg-color);
    border: 1px solid var(--rid-cod-border-color);
    border-radius: 8px;
    padding: 0;
    display: flex;
    align-items: center;
    box-shadow: none;
    height: 48px;
}

.rid-cod-form-modern .rid-cod-quantity-selector button {
    background: #f8f9fa;
    color: var(--rid-cod-accent-color);
    border: none;
    width: 40px;
    height: 46px;
    border-radius: 0;
    font-size: 20px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-quantity-selector button:first-child {
    border-radius: 8px 0 0 8px;
}

.rid-cod-form-modern .rid-cod-quantity-selector button:last-child {
    border-radius: 0 8px 8px 0;
}

.rid-cod-form-modern .rid-cod-quantity-selector button:hover {
    background: #e9ecef;
    transform: none;
}

.rid-cod-form-modern .rid-cod-quantity-selector input {
    border: none;
    border-left: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    background: white;
    text-align: center;
    width: 50px;
    font-weight: 600;
    color: #333;
    height: 46px;
}

/* Modern Form Variations Styling */
.rid-cod-form-modern .rid-cod-variations {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    margin-bottom: 20px;
}

.rid-cod-form-modern .rid-cod-variations .variations-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rid-cod-form-modern .rid-cod-variations .variation-row {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Modern Form - Variation labels to match image */
.rid-cod-form-modern .rid-cod-variations .variation-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    gap: 20px;
}

.rid-cod-form-modern .rid-cod-variations .variation-label-text {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    min-width: 60px;
}

.rid-cod-form-modern .rid-cod-variations .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
}

/* Color variations for modern form - match image exactly */
.rid-cod-form-modern .rid-cod-variations .variation-option.color-option {
    width: 30px;
    height: 30px;
    min-width: 30px;
    padding: 0;
    border-radius: 4px;
    border: 2px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    margin-left: 5px;
}

.rid-cod-form-modern .rid-cod-variations .variation-option.color-option:hover {
    transform: scale(1.1);
    border-color: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .rid-cod-variations .variation-option.color-option.selected {
    border-color: var(--rid-cod-accent-color);
    border-width: 2px;
    box-shadow: 0 0 0 1px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.5);
    transform: scale(1.1);
}

/* Size variations for modern form - match image exactly */
.rid-cod-form-modern .rid-cod-variations .variation-option:not(.color-option) {
    min-width: 35px;
    height: 35px;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: var(--rid-cod-primary-bg-color);
    color: var(--rid-cod-text-color);
    font-weight: 500;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
}

.rid-cod-form-modern .rid-cod-variations .variation-option:not(.color-option):hover {
    border-color: var(--rid-cod-accent-color);
    background: #f8f6fc;
    transform: scale(1.05);
}

.rid-cod-form-modern .rid-cod-variations .variation-option:not(.color-option).selected {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
    color: var(--rid-cod-button-text-color);
    font-weight: 600;
    border-width: 1px;
}

/* Modern Form Order Summary */
.rid-cod-form-modern #rid-cod-summary-wrapper {
    background: #f8f6fc;
    border: 1px solid var(--rid-cod-border-color);
    border-radius: 12px;
    margin-top: 25px;
    overflow: hidden;
}

.rid-cod-form-modern #rid-cod-summary-header {
    background: var(--rid-cod-accent-color);
    color: var(--rid-cod-button-text-color);
    padding: 15px 20px;
    border-bottom: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.rid-cod-form-modern #rid-cod-summary-header:hover {
    background: var(--rid-cod-button-bg-color);
}

.rid-cod-form-modern #rid-cod-summary-header h4 {
    color: var(--rid-cod-button-text-color);
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.rid-cod-form-modern #rid-cod-summary-header .rid-icon-cart {
    filter: brightness(0) invert(1);
}

.rid-cod-form-modern #rid-cod-summary-toggle {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708'/%3E%3C/svg%3E");
}

.rid-cod-form-modern #rid-cod-summary-content {
    background: white;
    padding: 20px;
}

.rid-cod-form-modern #rid-cod-summary-content table {
    width: 100%;
}

.rid-cod-form-modern #rid-cod-summary-content td {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
    color: #333;
}

.rid-cod-form-modern #rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    padding-top: 15px;
    font-weight: 700;
    color: var(--rid-cod-accent-color);
    font-size: 16px;
}

.rid-cod-form-modern .rid-summary-quantity {
    background: #e9ecef;
    color: #333;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 8px;
    font-weight: 600;
}

/* Modern Form - Delivery type in summary */
.rid-cod-form-modern .summary-delivery-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rid-cod-form-modern .summary-delivery-option {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    width: 100%;
}

.rid-cod-form-modern .summary-delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(177, 156, 217, 0.1);
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"] {
    display: none;
}

.rid-cod-form-modern .summary-delivery-option .summary-radio-custom {
    width: 14px;
    height: 14px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
}

.rid-cod-form-modern .summary-delivery-option .summary-delivery-text {
    font-weight: 500;
    color: #333;
}

/* Modern Form - Hide icons in inputs */
.rid-cod-form-modern .rid-input-icon {
    display: none !important;
}

/* Modern Form - Adjust input padding without icons */
.rid-cod-form-modern .rid-cod-field-group input,
.rid-cod-form-modern .rid-cod-field-group select {
    padding: 12px 15px !important;
    padding-right: 15px !important;
    padding-left: 15px !important;
}

/* Modern Form - Fix phone field RTL alignment */
.rid-cod-form-modern .rid-cod-field-group input[type="tel"] {
    direction: rtl !important;
    text-align: right !important;
}

.rid-cod-form-modern .rid-cod-field-group input[type="tel"]::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Modern Form - Fix placeholder visibility */
.rid-cod-form-modern .rid-cod-field-group input::placeholder {
    color: #999 !important;
    opacity: 1 !important;
    font-weight: 400 !important;
    transition: none !important;
}

/* Modern Form - Ensure placeholder stays visible */
.rid-cod-form-modern .rid-cod-field-group input:focus::placeholder {
    color: #ccc !important;
    opacity: 1 !important;
}

/* Modern Form - Override any conflicting styles */
.rid-cod-form-modern .rid-cod-field-group {
    position: relative;
}

.rid-cod-form-modern .rid-cod-field-group input {
    background: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    color: #333 !important;
    width: 100% !important;
    height: 48px !important;
    box-sizing: border-box !important;
}

/* Modern Form - Remove any floating label effects */
.rid-cod-form-modern .rid-cod-field-group label {
    display: none !important;
}

/* Modern Form - Ensure no label animations */
.rid-cod-form-modern .rid-cod-field-group input:focus + label,
.rid-cod-form-modern .rid-cod-field-group input:not(:placeholder-shown) + label {
    display: none !important;
}

/* Modern Form - Remove any floating label styles */
.rid-cod-form-modern .floating-label {
    display: none !important;
}

/* Modern Form - Ensure placeholders are always visible */
.rid-cod-form-modern .rid-cod-field-group input::-webkit-input-placeholder {
    color: #999 !important;
    opacity: 1 !important;
}

.rid-cod-form-modern .rid-cod-field-group input::-moz-placeholder {
    color: #999 !important;
    opacity: 1 !important;
}

.rid-cod-form-modern .rid-cod-field-group input:-ms-input-placeholder {
    color: #999 !important;
    opacity: 1 !important;
}

.rid-cod-form-modern .rid-cod-field-group input:-moz-placeholder {
    color: #999 !important;
    opacity: 1 !important;
}

/* Modern Form - Special styling for select dropdowns */
.rid-cod-form-modern .rid-cod-field-group select,
.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="%23b19cd9" viewBox="0 0 16 16"><path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    padding-left: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* Modern Form - Delivery section removed from top, now in summary */

/* Modern Form - Delivery type options in summary */
.rid-cod-form-modern .summary-delivery-options {
    display: flex;
    gap: 15px;
    align-items: center;
    justify-content: flex-start;
}

.rid-cod-form-modern .summary-delivery-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    gap: 6px;
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"] {
    display: none;
}

.rid-cod-form-modern .summary-radio-custom {
    width: 14px;
    height: 14px;
    border: 1px solid var(--rid-cod-accent-color);
    border-radius: 50%;
    position: relative;
    background: white;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom {
    background: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

.rid-cod-form-modern .summary-delivery-text {
    font-weight: 500;
    color: #333;
    font-size: 12px;
}

/* Modern Form - Enhanced summary delivery section */
.rid-cod-form-modern #rid-cod-summary-content #rid-cod-summary-delivery-type-row {
    background: #f8f6fc;
    border-radius: 6px;
}

.rid-cod-form-modern #rid-cod-summary-content #rid-cod-summary-delivery-type-row td {
    padding: 15px 12px;
    border-bottom: none;
}

.rid-cod-form-modern #rid-cod-summary-content #rid-cod-summary-delivery-type-row td:first-child {
    font-weight: 600;
    color: var(--rid-cod-accent-color);
}

/* Modern Form - Hover effects for delivery options in summary */
.rid-cod-form-modern .summary-delivery-option:hover .summary-radio-custom {
    border-color: var(--rid-cod-button-bg-color);
    transform: scale(1.1);
}

.rid-cod-form-modern .summary-delivery-option:hover .summary-delivery-text {
    color: var(--rid-cod-accent-color);
}

/* Modern Form - Notes field styling */
.rid-cod-form-modern .rid-cod-field-group textarea {
    grid-column: 1 / -1;
    height: 80px;
    resize: vertical;
    padding: 12px 15px;
}

/* Modern Form - Additional styling for perfect match */
.rid-cod-form-modern .rid-cod-field-group input::placeholder,
.rid-cod-form-modern .rid-cod-field-group select option:first-child {
    color: #999;
    font-weight: 400;
}

.rid-cod-form-modern .rid-cod-field-group input:focus::placeholder {
    color: #ccc;
}

/* Modern Form - Variation section spacing */
.rid-cod-form-modern .rid-cod-variations {
    margin: 20px 0;
    padding: 0;
    border: none;
}

/* Modern Form - Better spacing for variation rows */
.rid-cod-form-modern .rid-cod-variations .variation-row:not(:last-child) {
    margin-bottom: 20px;
}

/* Modern Form - Improve color swatch visibility */
.rid-cod-form-modern .rid-cod-variations .variation-option.color-option .color-swatch {
    width: 100%;
    height: 100%;
    border-radius: 4px;
}

/* Modern Form - Better button styling */
.rid-cod-form-modern #rid-cod-submit-btn {
    font-family: inherit;
    letter-spacing: 0.5px;
}

/* Modern Form - Ensure proper RTL layout */
.rid-cod-form-modern .rid-cod-variations .variation-boxes {
    direction: rtl;
}

.rid-cod-form-modern .rid-cod-variations .variation-option {
    margin-left: 8px;
    margin-right: 0;
}

.rid-cod-form-modern .rid-cod-variations .variation-option:last-child {
    margin-left: 0;
}

/* Modern Form - Dropdown variations styling */
.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 14px 16px;
    padding-left: 40px;
    font-size: 15px;
    font-weight: 500;
    color: #495057;
    background-color: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.04);
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select:focus {
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.15);
    outline: none;
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select:hover {
    border-color: #ced4da;
    background-color: #f8f9fa;
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations .variation-label {
    margin-bottom: 12px;
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations .attribute-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
}

/* Modern form - disabled options styling */
.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select option:disabled {
    color: #999 !important;
    background-color: #f8f9fa !important;
    font-style: italic;
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select.has-disabled-options {
    border-color: #ffc107;
    background-color: #fffbf0;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.1);
}

.rid-cod-form-modern.rid-cod-use-dropdown-variations .rid-cod-variations select.rid-cod-variation-select.has-disabled-options:focus {
    border-color: var(--rid-cod-accent-color);
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.15);
}

/* Modern Form - Perfect color matching */
.rid-cod-form-modern #rid-cod-checkout {
    border-color: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .rid-cod-field-group input:focus,
.rid-cod-form-modern .rid-cod-field-group select:focus {
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 2px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.15);
}

.rid-cod-form-modern .rid-cod-variations .variation-option.color-option.selected,
.rid-cod-form-modern .rid-cod-variations .variation-option:not(.color-option).selected {
    border-color: var(--rid-cod-accent-color);
}

.rid-cod-form-modern #rid-cod-summary-header {
    background-color: var(--rid-cod-accent-color);
}

.rid-cod-form-modern #rid-cod-summary-content tr:last-child td {
    color: var(--rid-cod-accent-color);
}

/* Modern Form - Final touches for exact match */
.rid-cod-form-modern .rid-cod-field-group select {
    color: #333;
    font-weight: 500;
}

.rid-cod-form-modern .rid-cod-field-group select option {
    color: #333;
    background: white;
}

.rid-cod-form-modern .rid-cod-variations .variation-label .attribute-label {
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

/* Modern Form - Ensure consistent spacing */
.rid-cod-form-modern .rid-cod-customer-info .rid-cod-field-group:nth-child(3),
.rid-cod-form-modern .rid-cod-customer-info .rid-cod-field-group:nth-child(4) {
    margin-top: 5px;
}

/* Modern Form - Perfect color swatches */
.rid-cod-form-modern .rid-cod-variations .variation-option.color-option {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.rid-cod-form-modern .rid-cod-variations .variation-option.color-option.selected {
    box-shadow: 0 0 0 2px var(--rid-cod-accent-color), 0 2px 8px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.3);
}

/* Modern Form - Enhanced summary styling */
.rid-cod-form-modern #rid-cod-summary-content .rid-summary-variation-details {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.rid-cod-form-modern #rid-cod-summary-content .variation-detail {
    background: #f8f6fc;
    color: var(--rid-cod-accent-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e9e4f0;
}

/* Modern Form - Delivery type options in summary */
.rid-cod-form-modern #rid-cod-summary-content #summary-delivery-type-row td {
    color: #666;
    font-size: 13px;
}

.rid-cod-form-modern .summary-delivery-options {
    display: flex;
    gap: 15px;
    align-items: center;
    justify-content: flex-start;
}

.rid-cod-form-modern .summary-delivery-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    color: #333;
    gap: 6px;
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"] {
    display: none;
}

.rid-cod-form-modern .summary-radio-custom {
    width: 14px;
    height: 14px;
    border: 1px solid var(--rid-cod-accent-color);
    border-radius: 50%;
    position: relative;
    background: white;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom {
    background: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .summary-delivery-option input[type="radio"]:checked + .summary-radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

.rid-cod-form-modern .summary-delivery-text {
    font-weight: 500;
    color: #333;
    font-size: 12px;
}

/* Modern Form - Enhanced summary delivery section */
.rid-cod-form-modern #rid-cod-summary-content #summary-delivery-type-row {
    background: #f8f6fc;
    border-radius: 6px;
}

.rid-cod-form-modern #rid-cod-summary-content #summary-delivery-type-row td {
    padding: 15px 12px;
    border-bottom: none;
}

.rid-cod-form-modern #rid-cod-summary-content #summary-delivery-type-row td:first-child {
    font-weight: 600;
    color: var(--rid-cod-accent-color);
}

/* Modern Form - Hover effects for delivery options in summary */
.rid-cod-form-modern .summary-delivery-option:hover .summary-radio-custom {
    border-color: var(--rid-cod-button-bg-color);
    transform: scale(1.1);
}

.rid-cod-form-modern .summary-delivery-option:hover .summary-delivery-text {
    color: var(--rid-cod-accent-color);
}

/* Modern Form - Better spacing for variation sections */
.rid-cod-form-modern .rid-cod-variations .variation-row {
    margin-bottom: 15px;
    padding-bottom: 0;
    border-bottom: none;
}

.rid-cod-form-modern .rid-cod-variations .variation-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Modern Form - Animation effects */
.rid-cod-form-modern .variation-option {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rid-cod-form-modern .variation-option:hover {
    transform: translateY(-2px);
}

.rid-cod-form-modern .variation-option.selected {
    animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Modern Form - Enhanced delivery options in main form */
.rid-cod-form-modern .rid-cod-delivery-section {
    background: linear-gradient(135deg, #f8f6fc 0%, #f0ebf8 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(177, 156, 217, 0.2);
}

.rid-cod-form-modern .rid-cod-delivery-section .delivery-label {
    color: var(--rid-cod-accent-color);
    font-weight: 700;
    font-size: 15px;
    margin-bottom: 15px;
    text-align: center;
}

.rid-cod-form-modern .rid-delivery-options {
    justify-content: center;
    gap: 25px;
    display: flex;
    flex-wrap: wrap;
}

.rid-cod-form-modern .delivery-option {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.rid-cod-form-modern .delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: rgba(177, 156, 217, 0.05);
}

.rid-cod-form-modern .delivery-option input[type="radio"] {
    display: none;
}

.rid-cod-form-modern .delivery-option .radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.rid-cod-form-modern .delivery-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
}

.rid-cod-form-modern .delivery-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

.rid-cod-form-modern .delivery-option .delivery-text {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option {
    background: white;
    border: 2px solid rgba(177, 156, 217, 0.3);
    border-radius: 10px;
    padding: 12px 18px;
    box-shadow: 0 2px 8px rgba(177, 156, 217, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(177, 156, 217, 0.1), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option:hover::before {
    left: 100%;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(177, 156, 217, 0.2);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option input[type="radio"]:checked + .radio-custom {
    background: var(--rid-cod-accent-color);
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(177, 156, 217, 0.2);
}

.rid-cod-form-modern .rid-delivery-options .delivery-text {
    font-weight: 600;
    font-size: 13px;
}

/* Modern Form - Summary update animation */
.rid-cod-form-modern #rid-cod-summary-content .variation-detail {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modern Form - Responsive adjustments */
@media (max-width: 768px) {
    .rid-cod-form-modern #rid-cod-checkout {
        margin: 10px;
        padding: 15px;
        border-radius: 15px;
    }

    .rid-cod-form-modern .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
        border-radius: 10px;
    }

    .rid-cod-form-modern .rid-cod-actions-row {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
        border-radius: 10px;
    }

    .rid-cod-form-modern .rid-cod-quantity {
        order: 1;
        justify-self: center;
    }

    .rid-cod-form-modern .rid-cod-submit {
        order: 2;
    }

    .rid-cod-form-modern .rid-cod-variations .variation-boxes {
        justify-content: center;
    }

    /* Fix summary table on mobile */
    .rid-cod-form-modern #rid-cod-summary-content {
        padding: 15px;
        overflow-x: auto;
    }

    .rid-cod-form-modern #rid-cod-summary-content table {
        width: 100%;
        border-collapse: collapse;
    }

    .rid-cod-form-modern #rid-cod-summary-content td {
        padding: 10px 5px;
        font-size: 13px;
        border-bottom: 1px solid #f0f0f0;
        word-wrap: break-word;
        vertical-align: top;
    }

    .rid-cod-form-modern #rid-cod-summary-content td:first-child {
        width: 60%;
        padding-right: 10px;
    }

    .rid-cod-form-modern #rid-cod-summary-content td:last-child {
        width: 40%;
        text-align: left;
        padding-left: 10px;
    }

    /* Delivery options responsive */
    .rid-cod-form-modern .rid-delivery-options {
        flex-direction: column;
        gap: 10px;
    }

    .rid-cod-form-modern .delivery-option {
        min-width: auto;
        width: 100%;
        justify-content: flex-start;
        padding: 15px;
    }

    .rid-cod-form-modern .summary-delivery-options {
        flex-direction: column;
        gap: 8px;
    }

    .rid-cod-form-modern .summary-delivery-option {
        width: 100%;
        justify-content: flex-start;
        padding: 10px;
    }

    /* Fix summary header */
    .rid-cod-form-modern #rid-cod-summary-header {
        padding: 15px;
    }

    .rid-cod-form-modern #rid-cod-summary-header h4 {
        font-size: 14px;
        margin: 0;
    }
}



/* Default variation size (medium) */
.rid-cod-variation-option,
.variation-option {
    min-width: 45px;
    min-height: 45px;
    font-size: 14px;
}

/* Variation size classes */
.rid-variation-size-small .rid-cod-variation-option,
.rid-variation-size-small .variation-option {
    min-width: 35px !important;
    min-height: 35px !important;
    font-size: 12px !important;
}

.rid-variation-size-medium .rid-cod-variation-option,
.rid-variation-size-medium .variation-option {
    min-width: 45px !important;
    min-height: 45px !important;
    font-size: 14px !important;
}

.rid-variation-size-large .rid-cod-variation-option,
.rid-variation-size-large .variation-option {
    min-width: 55px !important;
    min-height: 55px !important;
    font-size: 16px !important;
}

.rid-variation-size-extra-large .rid-cod-variation-option,
.rid-variation-size-extra-large .variation-option {
    min-width: 65px !important;
    min-height: 65px !important;
    font-size: 18px !important;
}

/* Additional selectors for variation boxes */
.rid-variation-size-small .variation-boxes .variation-option {
    min-width: 35px !important;
    min-height: 35px !important;
    font-size: 12px !important;
}

.rid-variation-size-medium .variation-boxes .variation-option {
    min-width: 45px !important;
    min-height: 45px !important;
    font-size: 14px !important;
}

.rid-variation-size-large .variation-boxes .variation-option {
    min-width: 55px !important;
    min-height: 55px !important;
    font-size: 16px !important;
}

.rid-variation-size-extra-large .variation-boxes .variation-option {
    min-width: 65px !important;
    min-height: 65px !important;
    font-size: 18px !important;
}

/* Color names display - show names instead of swatches */
.rid-show-color-names .rid-cod-variation-option.color-option,
.rid-show-color-names .variation-option.color-option {
    border-radius: 8px !important;
    background: #f8f9fa !important;
    border: 2px solid #dee2e6 !important;
    color: #495057 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 10px 14px !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
    min-height: 44px !important;
    white-space: nowrap !important;
    font-size: 16px !important;
    margin: 4px !important;
}

.rid-show-color-names .rid-cod-variation-option.color-option:hover,
.rid-show-color-names .variation-option.color-option:hover {
    background: #e9ecef;
    border-color: var(--rid-cod-accent-color);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.rid-show-color-names .rid-cod-variation-option.color-option.selected,
.rid-show-color-names .variation-option.color-option.selected {
    background: var(--rid-cod-accent-color) !important;
    color: white !important;
    border-color: var(--rid-cod-accent-color) !important;
    box-shadow: 0 2px 8px rgba(106, 61, 232, 0.3) !important;
}

/* Force spacing for color name containers */
.rid-show-color-names .rid-cod-variations .variation-option.color-option {
    margin: 4px !important;
    padding: 10px 14px !important;
}

.rid-show-color-names .rid-cod-variations {
    gap: 4px !important;
}

/* Hide color swatch when showing names */
.rid-show-color-names .rid-cod-variation-option.color-option .color-swatch,
.rid-show-color-names .variation-option.color-option .color-swatch {
    display: none !important;
}

/* Show color names when enabled */
.rid-show-color-names .rid-cod-variation-option.color-option .color-name,
.rid-show-color-names .variation-option.color-option .color-name {
    display: block !important;
    font-size: 16px;
    line-height: 1.3;
    font-weight: 600;
}

/* Hide color names by default (when showing swatches) */
.rid-cod-variation-option.color-option .color-name,
.variation-option.color-option .color-name {
    display: none;
}

/* Ensure color swatches are visible by default */
.rid-cod-variation-option.color-option .color-swatch,
.variation-option.color-option .color-swatch {
    display: block;
}



/* Responsive adjustments for color names */
@media (max-width: 768px) {
.rid-variation-size-extra-large.rid-show-color-names .rid-cod-variation-option.color-option,
.rid-variation-size-extra-large.rid-show-color-names .variation-option.color-option {
    padding: 14px 20px;
    min-height: 56px;
    font-size: 20px;
    margin: 6px;
}

/* Third Form Variation Size Classes - Additional selectors for better compatibility */
.rid-cod-form-third.rid-variation-size-small .variation-option,
.rid-cod-form-third.rid-variation-size-small .rid-cod-variation-option {
    min-width: 35px !important;
    min-height: 35px !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
}

.rid-cod-form-third.rid-variation-size-medium .variation-option,
.rid-cod-form-third.rid-variation-size-medium .rid-cod-variation-option {
    min-width: 45px !important;
    min-height: 45px !important;
    font-size: 14px !important;
    padding: 10px 16px !important;
}

.rid-cod-form-third.rid-variation-size-large .variation-option,
.rid-cod-form-third.rid-variation-size-large .rid-cod-variation-option {
    min-width: 55px !important;
    min-height: 55px !important;
    font-size: 16px !important;
    padding: 14px 20px !important;
}

.rid-cod-form-third.rid-variation-size-extra-large .variation-option,
.rid-cod-form-third.rid-variation-size-extra-large .rid-cod-variation-option {
    min-width: 65px !important;
    min-height: 65px !important;
    font-size: 18px !important;
    padding: 16px 24px !important;
}

/* Third Form Color Options - Size Specific */
.rid-cod-form-third.rid-variation-size-small .variation-option.color-option,
.rid-cod-form-third.rid-variation-size-small .rid-cod-variation-option.color-option {
    width: 28px !important;
    height: 28px !important;
}

.rid-cod-form-third.rid-variation-size-medium .variation-option.color-option,
.rid-cod-form-third.rid-variation-size-medium .rid-cod-variation-option.color-option {
    width: 35px !important;
    height: 35px !important;
}

.rid-cod-form-third.rid-variation-size-large .variation-option.color-option,
.rid-cod-form-third.rid-variation-size-large .rid-cod-variation-option.color-option {
    width: 45px !important;
    height: 45px !important;
}

.rid-cod-form-third.rid-variation-size-extra-large .variation-option.color-option,
.rid-cod-form-third.rid-variation-size-extra-large .rid-cod-variation-option.color-option {
    width: 55px !important;
    height: 55px !important;
}

/* All Form Styles - Variation Size Classes */
.rid-cod-form-classic.rid-variation-size-small .variation-option,
.rid-cod-form-classic.rid-variation-size-small .rid-cod-variation-option,
.rid-cod-form-modern.rid-variation-size-small .variation-option,
.rid-cod-form-modern.rid-variation-size-small .rid-cod-variation-option,
.rid-cod-form-second.rid-variation-size-small .variation-option,
.rid-cod-form-second.rid-variation-size-small .rid-cod-variation-option {
    min-width: 35px !important;
    min-height: 35px !important;
    font-size: 12px !important;
}

.rid-cod-form-classic.rid-variation-size-medium .variation-option,
.rid-cod-form-classic.rid-variation-size-medium .rid-cod-variation-option,
.rid-cod-form-modern.rid-variation-size-medium .variation-option,
.rid-cod-form-modern.rid-variation-size-medium .rid-cod-variation-option,
.rid-cod-form-second.rid-variation-size-medium .variation-option,
.rid-cod-form-second.rid-variation-size-medium .rid-cod-variation-option {
    min-width: 45px !important;
    min-height: 45px !important;
    font-size: 14px !important;
}

.rid-cod-form-classic.rid-variation-size-large .variation-option,
.rid-cod-form-classic.rid-variation-size-large .rid-cod-variation-option,
.rid-cod-form-modern.rid-variation-size-large .variation-option,
.rid-cod-form-modern.rid-variation-size-large .rid-cod-variation-option,
.rid-cod-form-second.rid-variation-size-large .variation-option,
.rid-cod-form-second.rid-variation-size-large .rid-cod-variation-option {
    min-width: 55px !important;
    min-height: 55px !important;
    font-size: 16px !important;
}

.rid-cod-form-classic.rid-variation-size-extra-large .variation-option,
.rid-cod-form-classic.rid-variation-size-extra-large .rid-cod-variation-option,
.rid-cod-form-modern.rid-variation-size-extra-large .variation-option,
.rid-cod-form-modern.rid-variation-size-extra-large .rid-cod-variation-option,
.rid-cod-form-second.rid-variation-size-extra-large .variation-option,
.rid-cod-form-second.rid-variation-size-extra-large .rid-cod-variation-option {
    min-width: 65px !important;
    min-height: 65px !important;
    font-size: 18px !important;
}

/* Ensure color names work with different variation sizes */
.rid-variation-size-small.rid-show-color-names .rid-cod-variation-option.color-option,
.rid-variation-size-small.rid-show-color-names .variation-option.color-option {
    padding: 6px 10px;
    min-height: 36px;
    font-size: 14px;
    margin: 3px;
}

.rid-variation-size-large.rid-show-color-names .rid-cod-variation-option.color-option,
.rid-variation-size-large.rid-show-color-names .variation-option.color-option {
    padding: 12px 16px;
    min-height: 50px;
    font-size: 18px;
    margin: 5px;
}

.rid-variation-size-extra-large.rid-show-color-names .rid-cod-variation-option.color-option,
.rid-variation-size-extra-large.rid-show-color-names .variation-option.color-option {
    padding: 14px 20px;
    min-height: 56px;
    font-size: 20px;
    margin: 6px;
}

/* Notes field styling */
.rid-cod-field-group textarea {
    min-height: 80px;
    resize: vertical;
    font-family: inherit;
}

/* Enhanced JavaScript interactions */
.rid-cod-field-group.focused {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

.floating-label {
    position: absolute;
    top: 15px;
    right: 15px;
    color: #999;
    transition: all 0.3s ease;
    pointer-events: none;
    font-size: 16px;
    z-index: 1;
}

.floating-label.active {
    top: -8px;
    font-size: 12px;
    color: var(--rid-cod-accent-color);
    background: white;
    padding: 0 5px;
}

#rid-cod-submit-btn.pressed {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

.rid-cod-quantity-selector button.clicked {
    transform: scale(1.2);
    transition: transform 0.15s ease;
}

.character-counter {
    font-size: 12px;
    color: #666;
    text-align: left;
    margin-top: 5px;
    direction: ltr;
}

/* Additional responsive fixes for modern form */
@media (max-width: 768px) {
    .rid-cod-form-modern #rid-cod-checkout {
        margin: 5px;
        padding: 15px;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(var(--rid-cod-accent-color-rgb, 177, 156, 217), 0.1);
    }

    .rid-cod-form-modern .rid-cod-customer-info {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .rid-cod-form-modern .rid-cod-actions-row {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
        border-radius: 10px;
    }

    .rid-cod-form-modern #rid-cod-submit-btn {
        width: 100%;
        padding: 15px;
        font-size: 15px;
    }

    /* Fix summary wrapper on mobile */
    .rid-cod-form-modern #rid-cod-summary-wrapper {
        margin: 15px 0;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Remove problematic borders and lines */
    .rid-cod-form-modern #rid-cod-summary-content table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .rid-cod-form-modern #rid-cod-summary-content tr {
        border: none;
    }

    .rid-cod-form-modern #rid-cod-summary-content td {
        border-left: none;
        border-right: none;
        border-top: none;
        border-bottom: 1px solid #f5f5f5;
        padding: 12px 8px;
    }

    .rid-cod-form-modern #rid-cod-summary-content tr:last-child td {
        border-bottom: none;
        font-weight: 700;
        color: var(--rid-cod-accent-color);
        padding-top: 15px;
    }

    /* Improve text wrapping */
    .rid-cod-form-modern .product-name {
        font-size: 13px;
        line-height: 1.3;
        word-break: break-word;
    }

    .rid-cod-form-modern .rid-summary-variation-details {
        font-size: 11px;
        margin-top: 5px;
        line-height: 1.2;
    }

    .floating-label {
        right: 10px;
        top: 12px;
    }

    .floating-label.active {
        top: -6px;
        font-size: 11px;
    }
}/* Modern
 Form - Delivery Section (Fixed to match basic structure) */
.rid-cod-form-modern .rid-cod-delivery-section {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #f8f6fc 0%, #f0ebf8 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(177, 156, 217, 0.2);
    box-shadow: 0 2px 8px rgba(177, 156, 217, 0.1);
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-delivery-section:hover {
    box-shadow: 0 4px 16px rgba(177, 156, 217, 0.15);
    border-color: rgba(177, 156, 217, 0.3);
}

.rid-cod-form-modern .rid-cod-delivery-section .delivery-label {
    display: block;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--rid-cod-accent-color);
    font-size: 15px;
    text-align: center;
}

.rid-cod-form-modern .rid-delivery-options {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: var(--rid-cod-text-color);
    padding: 12px 16px;
    border: 2px solid rgba(177, 156, 217, 0.3);
    border-radius: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f6fc 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    min-width: 140px;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 2px 4px rgba(177, 156, 217, 0.1);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: linear-gradient(135deg, #f0ebf8 0%, #e6dff5 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(177, 156, 217, 0.2);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option input[type="radio"] {
    display: none;
}

.rid-cod-form-modern .rid-delivery-options .radio-custom {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(177, 156, 217, 0.5);
    border-radius: 50%;
    position: relative;
    background: white;
    transition: all 0.3s ease;
    flex-shrink: 0;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--rid-cod-accent-color);
    background: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(177, 156, 217, 0.2);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option input[type="radio"]:checked ~ .delivery-text {
    color: var(--rid-cod-accent-color);
    font-weight: 700;
}

.rid-cod-form-modern .rid-delivery-options .delivery-text {
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
}

.rid-cod-form-modern .rid-delivery-options .delivery-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: linear-gradient(135deg, #f0ebf8 0%, #e6dff5 100%);
    box-shadow: 0 4px 16px rgba(177, 156, 217, 0.25);
    transform: translateY(-1px);
}

.rid-cod-form-modern .rid-delivery-options .delivery-option.selected .delivery-text {
    color: var(--rid-cod-accent-color);
    font-weight: 700;
}

/* Modern Form - Animation for delivery option selection */
.rid-cod-form-modern .rid-delivery-options .delivery-option.just-selected {
    animation: modernSelectPulse 0.4s ease-out;
}

@keyframes modernSelectPulse {
    0% { 
        transform: scale(1) translateY(0); 
        box-shadow: 0 2px 4px rgba(177, 156, 217, 0.1);
    }
    50% { 
        transform: scale(1.05) translateY(-3px); 
        box-shadow: 0 8px 20px rgba(177, 156, 217, 0.3);
    }
    100% { 
        transform: scale(1) translateY(-1px); 
        box-shadow: 0 4px 16px rgba(177, 156, 217, 0.25);
    }
}

/* Modern Form - Responsive delivery options */
@media (max-width: 768px) {
    .rid-cod-form-modern .rid-cod-delivery-section {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .rid-cod-form-modern .rid-delivery-options {
        flex-direction: column;
        gap: 10px;
    }
    
    .rid-cod-form-modern .rid-delivery-options .delivery-option {
        width: 100%;
        min-width: auto;
        justify-content: flex-start;
        padding: 15px;
    }
}

/* Second Form Style - Inspired by order-form design */
.rid-cod-form-second #rid-cod-checkout {
    position: relative;
    padding: 30px;
    border: 2px solid #259bea;
    border-radius: 6px;
    margin-top: 50px;
    background: var(--rid-cod-primary-bg-color);
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

/* Ensure the form container itself takes full width on mobile */
@media (max-width: 768px) {
    .rid-cod-form-second {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden;
    }

    /* Add subtle side borders using box-shadow instead of border */
    .rid-cod-form-second #rid-cod-checkout {
        box-shadow:
            2px 0 0 0 #259bea,  /* Right border */
            -2px 0 0 0 #259bea, /* Left border */
            0 2px 4px rgba(37, 155, 234, 0.1); /* Subtle shadow */
    }
}

.rid-cod-form-second .rid-cod-title {
    text-align: center;
}

.rid-cod-form-second .rid-cod-title h3 {
    padding: 0 20px;
    display: inline-block;
    color: #000;
    font-size: 16px;
    margin-bottom: 25px;
    font-weight: 500;
}

.rid-cod-form-second #rid-cod-form {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: 1fr;
}

.rid-cod-form-second .rid-cod-customer-info {
    display: grid;
    grid-gap: 15px;
    grid-template-columns: repeat(2, 1fr);
    /* Ensure minimum column width for readability */
    min-width: 0;
}

.rid-cod-form-second #rid-cod-form input[type="text"],
.rid-cod-form-second #rid-cod-form input[type="tel"],
.rid-cod-form-second #rid-cod-form input[type="email"],
.rid-cod-form-second #rid-cod-form select,
.rid-cod-form-second #rid-cod-form textarea {
    border: 2px solid #bce0f7;
    height: 50px;
    border-radius: 3px;
    padding: 12px 15px; /* No extra padding for icons */
    background: #ffffff;
    font-size: 14px;
    color: #333;
    transition: border-color 0.3s ease;
    width: 100%;
    box-sizing: border-box;
    min-width: 0; /* Prevent overflow on small screens */
}

/* Second Form - Fix phone field RTL alignment */
.rid-cod-form-second #rid-cod-form input[type="tel"] {
    direction: rtl !important;
    text-align: right !important;
}

.rid-cod-form-second #rid-cod-form input[type="tel"]::placeholder {
    direction: rtl !important;
    text-align: right !important;
    unicode-bidi: plaintext !important;
}

/* Remove icons from second form inputs */
.rid-cod-form-second .rid-cod-field-with-icon .rid-input-icon {
    display: none !important;
}

.rid-cod-form-second #rid-cod-form input:valid,
.rid-cod-form-second #rid-cod-form select:valid {
    border: 2px solid #69bf29 !important;
    outline: none;
}

.rid-cod-form-second #rid-cod-form input:required:focus:invalid,
.rid-cod-form-second #rid-cod-form select:required:focus:invalid {
    border: 2px solid #d65d67 !important;
    outline: none;
}

.rid-cod-form-second .rid-cod-field-group {
    margin: 0;
}

/* Actions row styling for second form */
.rid-cod-form-second .rid-cod-actions-row {
    display: grid;
    grid-gap: 12px;
    grid-template-columns: 120px 1fr;
    margin-top: 10px;
}

.rid-cod-form-second #rid-cod-submit-btn {
    background-color: #259bea;
    border: none;
    color: #fff !important;
    height: 50px;
    line-height: 50px;
    padding: 0;
    overflow: hidden;
    border-radius: 3px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rid-cod-form-second #rid-cod-submit-btn:hover {
    background-color: #1e7bb8;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 155, 234, 0.3);
}

/* Quantity selector for second form */
.rid-cod-form-second .rid-cod-quantity-selector {
    display: grid;
    grid-template-columns: 35px 50px 35px;
    border: 2px solid #bce0f7;
    border-radius: 3px;
    height: 50px;
    overflow: hidden;
}

.rid-cod-form-second #rid-cod-decrease,
.rid-cod-form-second #rid-cod-increase,
.rid-cod-form-second .qty-btn {
    color: #333;
    font-size: 18px;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
    background: #f8f8f8;
    border: none;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.rid-cod-form-second #rid-cod-decrease,
.rid-cod-form-second .qty-btn:first-child {
    border-right: 1px solid #bce0f7;
}

.rid-cod-form-second #rid-cod-increase,
.rid-cod-form-second .qty-btn:last-child {
    border-left: 1px solid #bce0f7;
}

.rid-cod-form-second #rid-cod-decrease:hover,
.rid-cod-form-second #rid-cod-increase:hover,
.rid-cod-form-second .qty-btn:hover {
    background: #e9ecef;
    color: #259bea;
}

.rid-cod-form-second #rid-cod-quantity-input,
.rid-cod-form-second .qty-input {
    text-align: center;
    border: none;
    background: #ffffff;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 46px;
    outline: none;
}

/* Select styling for second form */
.rid-cod-form-second #rid-cod-form select {
    cursor: pointer;
    color: #888;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
    background-size: 9px;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    padding-right: 28px !important;
}

/* Order summary styling for second form */
.rid-cod-form-second #rid-cod-summary-wrapper {
    border: 2px solid #bce0f7;
    border-radius: 3px;
    margin-top: 15px;
    background: #f0f9ff;
}

.rid-cod-form-second #rid-cod-summary-header {
    height: 50px;
    cursor: pointer;
    border-bottom: 2px solid #bce0f7;
    background: #f0f9ff;
    padding: 0 10px;
    margin-bottom: 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.rid-cod-form-second #rid-cod-summary-header h4 {
    line-height: 50px;
    font-size: 16px;
    margin: 0;
    color: #333;
}

.rid-cod-form-second #rid-cod-summary-header h4 i {
    font-size: 20px;
    color: #bce0f7;
    margin-right: 5px;
}

.rid-cod-form-second #rid-cod-summary-toggle {
    color: #bce0f7;
    line-height: 50px;
    font-size: 25px;
}

.rid-cod-form-second #rid-cod-summary-content {
    background: #f0f9ff;
    padding: 10px;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}

.rid-cod-form-second #rid-cod-summary-content table {
    border: none;
    margin-bottom: 0;
    width: 100%;
}

.rid-cod-form-second #rid-cod-summary-content td {
    border: none;
    border-bottom: 2px dotted #bce0f7;
    padding: 8px 0;
    font-size: 14px;
}

.rid-cod-form-second #rid-cod-summary-content tr:last-child td {
    font-weight: bold;
    border-bottom: 0;
}

.rid-cod-form-second #rid-cod-summary-content tr:last-child td:last-child {
    color: #259bea;
}

/* Variations styling for second form - Compact design */
.rid-cod-form-second .rid-cod-variations {
    /* Compact variation styles for second form */
    background: #f8f9fa !important;
    border: 1px solid #bce0f7 !important;
    border-radius: 5px !important;
    padding: 10px !important;
    margin-bottom: 10px !important;
    box-shadow: none !important;
    transition: all 0.3s ease !important;
}

.rid-cod-form-second .rid-cod-variations:hover {
    border-color: #259bea !important;
}

/* Compact variation boxes for second form */
.rid-cod-form-second .rid-cod-variations .variation-boxes {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 6px !important;
    margin-top: 5px !important;
    margin-bottom: 0 !important;
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
}

/* Compact variation options for second form */
.rid-cod-form-second .rid-cod-variations .variation-option {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 60px !important;
    padding: 6px 12px !important;
    background: #ffffff !important;
    border: 1px solid #bce0f7 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #495057 !important;
    text-align: center !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    overflow: hidden !important;
    margin: 0 !important;
    height: 28px !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option:hover {
    border-color: #259bea !important;
    background: #f0f9ff !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option.selected {
    border-color: #259bea !important;
    background: #259bea !important;
    color: #ffffff !important;
    font-weight: 600 !important;
}

/* Compact color variations for second form */
.rid-cod-form-second .rid-cod-variations .variation-option.color-option {
    min-width: 32px !important;
    min-height: 32px !important;
    padding: 2px !important;
    border-radius: 50% !important;
    width: 32px !important;
    height: 32px !important;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option.color-option .color-swatch {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option.color-option .color-name {
    display: none !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option.color-option.selected {
    border-color: #259bea !important;
    box-shadow: 0 0 0 2px #259bea !important;
}

.rid-cod-form-second .rid-cod-variations .variation-option.color-option.selected:after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 12px !important;
    height: 12px !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z'/%3E%3C/svg%3E") !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.5)) !important;
}

/* Compact variation labels for second form */
.rid-cod-form-second .rid-cod-variations .variations td.label {
    font-size: 12px !important;
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 5px 0 !important;
    margin-bottom: 5px !important;
}

.rid-cod-form-second .rid-cod-variations .variations td.value {
    padding: 0 !important;
}

/* Animation for submit button */
.rid-cod-form-second.animate-button #rid-cod-submit-btn:not(.atc-buy-button) {
    animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
    transform: translate3d(0, 0, 0);
    perspective: 1000px;
}

@keyframes shaking {
    0%, 100% {
        transform: translate3d(0, 0, 0);
    }
    4%, 46% {
        transform: translate3d(-1px, 0, 0);
    }
    8%, 42% {
        transform: translate3d(2px, 0, 0);
    }
    12%, 37% {
        transform: translate3d(-3px, 0, 0);
    }
    16%, 33% {
        transform: translate3d(3px, 0, 0);
    }
}

/* Responsive design for second form - Keep grid layout on mobile */
@media (max-width: 768px) {
    .rid-cod-form-second #rid-cod-checkout {
        padding: 15px 10px;
        margin: 10px 0; /* Remove horizontal margins */
        border-radius: 0; /* Remove border radius on mobile for full width */
        width: 100%; /* Use full width instead of viewport width */
        max-width: 100%;
        border-left: none; /* Remove left border to prevent overflow */
        border-right: none; /* Remove right border to prevent overflow */
        border-top: 2px solid #259bea; /* Keep top border */
        border-bottom: 2px solid #259bea; /* Keep bottom border */
        box-sizing: border-box;
    }

    /* Keep 2-column grid but with smaller gap and ensure full width */
    .rid-cod-form-second .rid-cod-customer-info {
        grid-template-columns: repeat(2, 1fr);
        grid-gap: 8px;
        width: 100%;
    }

    /* Adjust input height for mobile */
    .rid-cod-form-second #rid-cod-form input[type="text"],
    .rid-cod-form-second #rid-cod-form input[type="tel"],
    .rid-cod-form-second #rid-cod-form input[type="email"],
    .rid-cod-form-second #rid-cod-form select,
    .rid-cod-form-second #rid-cod-form textarea {
        height: 45px;
        font-size: 13px;
        padding: 10px 12px;
    }

    /* Optimize actions row layout for mobile */
    .rid-cod-form-second .rid-cod-actions-row {
        grid-template-columns: 100px 1fr;
        grid-gap: 8px;
    }

    .rid-cod-form-second #rid-cod-submit-btn {
        height: 45px;
        font-size: 14px;
        padding: 0 10px;
    }

    .rid-cod-form-second .rid-cod-quantity-selector {
        height: 45px;
        grid-template-columns: 30px 40px 30px;
    }

    .rid-cod-form-second #rid-cod-decrease,
    .rid-cod-form-second #rid-cod-increase,
    .rid-cod-form-second .qty-btn {
        line-height: 41px;
        font-size: 16px;
    }

    .rid-cod-form-second #rid-cod-quantity-input,
    .rid-cod-form-second .qty-input {
        line-height: 41px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .rid-cod-form-second #rid-cod-checkout {
        padding: 12px 8px;
        margin: 5px 0; /* Remove horizontal margins */
        border-radius: 0; /* Remove border radius for full width */
        width: 100%; /* Use full width instead of viewport width */
        max-width: 100%;
        border-left: none; /* Remove left border to prevent overflow */
        border-right: none; /* Remove right border to prevent overflow */
        border-top: 2px solid #259bea; /* Keep top border */
        border-bottom: 2px solid #259bea; /* Keep bottom border */
        box-sizing: border-box;
    }

    /* Keep 2-column grid but with smaller inputs and ensure full width */
    .rid-cod-form-second .rid-cod-customer-info {
        grid-gap: 6px;
        width: 100%;
    }

    .rid-cod-form-second #rid-cod-form input[type="text"],
    .rid-cod-form-second #rid-cod-form input[type="tel"],
    .rid-cod-form-second #rid-cod-form input[type="email"],
    .rid-cod-form-second #rid-cod-form select,
    .rid-cod-form-second #rid-cod-form textarea {
        height: 42px;
        font-size: 12px;
        padding: 8px 10px;
    }

    /* Optimize actions row for small screens */
    .rid-cod-form-second .rid-cod-actions-row {
        grid-template-columns: 90px 1fr;
        grid-gap: 6px;
    }

    .rid-cod-form-second #rid-cod-submit-btn {
        height: 42px;
        font-size: 13px;
        padding: 0 8px;
    }

    .rid-cod-form-second .rid-cod-quantity-selector {
        height: 42px;
        grid-template-columns: 28px 34px 28px;
    }

    .rid-cod-form-second #rid-cod-decrease,
    .rid-cod-form-second #rid-cod-increase,
    .rid-cod-form-second .qty-btn {
        line-height: 38px;
        font-size: 14px;
    }

    .rid-cod-form-second #rid-cod-quantity-input,
    .rid-cod-form-second .qty-input {
        line-height: 38px;
        font-size: 13px;
    }

    /* Adjust title for small screens */
    .rid-cod-form-second .rid-cod-title h3 {
        font-size: 14px;
        padding: 0 10px;
        margin-bottom: 20px;
    }
}

/* Extra small screens (very small phones) */
@media (max-width: 320px) {
    .rid-cod-form-second #rid-cod-checkout {
        padding: 10px 6px;
        margin: 0; /* Remove all margins */
        border-radius: 0; /* Remove border radius for full width */
        width: 100%; /* Use full width instead of viewport width */
        max-width: 100%;
        border-left: none; /* Remove left border to prevent overflow */
        border-right: none; /* Remove right border to prevent overflow */
        border-top: 2px solid #259bea; /* Keep top border */
        border-bottom: 2px solid #259bea; /* Keep bottom border */
        box-sizing: border-box;
    }

    .rid-cod-form-second .rid-cod-customer-info {
        grid-gap: 6px;
    }

    .rid-cod-form-second #rid-cod-form input[type="text"],
    .rid-cod-form-second #rid-cod-form input[type="tel"],
    .rid-cod-form-second #rid-cod-form input[type="email"],
    .rid-cod-form-second #rid-cod-form select,
    .rid-cod-form-second #rid-cod-form textarea {
        height: 40px;
        font-size: 11px;
        padding: 6px 8px;
    }

    .rid-cod-form-second .rid-cod-actions-row {
        grid-template-columns: 80px 1fr;
        grid-gap: 5px;
    }

    .rid-cod-form-second #rid-cod-submit-btn {
        height: 40px;
        font-size: 12px;
        padding: 0 6px;
    }

    .rid-cod-form-second .rid-cod-quantity-selector {
        height: 40px;
        grid-template-columns: 25px 30px 25px;
    }

    .rid-cod-form-second #rid-cod-decrease,
    .rid-cod-form-second #rid-cod-increase,
    .rid-cod-form-second .qty-btn {
        line-height: 36px;
        font-size: 14px;
    }

    .rid-cod-form-second #rid-cod-quantity-input,
    .rid-cod-form-second .qty-input {
        line-height: 36px;
        font-size: 12px;
    }

    .rid-cod-form-second .rid-cod-title h3 {
        font-size: 13px;
        padding: 0 8px;
        margin-bottom: 15px;
    }
}

/* RTL support for second form */
.rtl.rid-cod-form-second #rid-cod-form select {
    background-position: left 0.75rem center;
    padding-left: 28px !important;
    padding-right: 15px !important;
}

.rtl.rid-cod-form-second #rid-cod-decrease {
    border-right: 2px solid #bce0f7;
    border-left: 0;
}

.rtl.rid-cod-form-second #rid-cod-increase {
    border-left: 2px solid #bce0f7;
    border-right: 0;
}

/* Ensure full width on mobile devices */
@media (max-width: 768px) {
    .rid-cod-form-second .rid-cod-form-container {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Remove any container margins that might cause side gaps */
    .rid-cod-form-second {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }
}

/* Fix for delivery section visibility issue in modern form */
.rid-cod-form-modern .rid-cod-delivery-section,
.rid-cod-form-modern .rid-cod-delivery-section *,
.rid-cod-form-modern .rid-delivery-options,
.rid-cod-form-modern .rid-delivery-options * {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    z-index: 999999 !important;
    isolation: isolate !important;
    visibility: visible !important;
}

/* Ensure delivery options are properly displayed */
.rid-cod-form-modern .rid-delivery-options {
    display: flex !important;
}

.rid-cod-form-modern .delivery-option {
    display: flex !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Override any conflicting styles that might hide the delivery section */
.rid-cod-form-modern .rid-cod-delivery-section {
    position: relative !important;
    background: #f9f9f9 !important;
    border: 1px solid var(--rid-cod-border-color) !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
}
