# إصلاح مشاكل إرسال البيانات إلى Google Sheets

## المشاكل المحلولة
1. **الملاحظات**: كانت الملاحظات التي يدخلها العملاء في النموذج لا يتم إرسالها إلى Google Sheets عند تفعيل الميزة.
2. **الولايات**: عند تفعيل ميزة "تحويل الولايات والبلديات إلى حقول نصية"، الولايات لا يتم إرسالها إلى Google Sheets (البلديات كانت تعمل).

## أسباب المشاكل

### مشكلة الملاحظات
1. الملاحظات كانت يتم حفظها كـ **order notes** داخلية باستخدام `add_order_note()`
2. عند إرسال البيانات إلى Google Sheets، الكود كان يحاول الحصول على الملاحظات باستخدام `get_customer_note()`
3. `get_customer_note()` لا يعيد order notes، بل فقط customer notes المحفوظة بطريقة مختلفة

### مشكلة الولايات
1. عند تفعيل ميزة النص، الولاية تصبح حقل نص بدلاً من قائمة منسدلة
2. القيمة المرسلة في `$_POST['state']` تصبح النص المكتوب وليس كود الولاية
3. عند استخراج البيانات لـ Google Sheets، الكود كان يعتمد فقط على `$order->get_billing_state()` دون fallback

## الحل المطبق

### 1. تحسين حفظ الملاحظات في الطلبات الجديدة
في ملف `includes/class-rid-cod-ajax.php` في دالة `process_order()`:

```php
// فصل الملاحظات إلى customer notes و internal notes
$customer_notes = '';
$internal_notes = '';

if (!empty($_POST['notes'])) {
    $customer_notes = sanitize_textarea_field($_POST['notes']);
    $internal_notes .= $customer_notes . "\n";
}

// حفظ الملاحظات كـ customer note للتوافق مع Google Sheets
if (!empty(trim($customer_notes))) {
    $order->set_customer_note(trim($customer_notes));
    
    // حفظ إضافي كـ meta data للأمان
    update_post_meta($order_id, '_rid_cod_customer_notes', trim($customer_notes));
}

// حفظ الملاحظات الداخلية مع تفاصيل التوصيل
if (!empty(trim($internal_notes))) {
    $order->add_order_note(trim($internal_notes), 0, false);
}
```

### 2. تحسين استخراج الملاحظات لـ Google Sheets
في نفس الملف في دالة `handle_scheduled_google_sheets_sync()`:

```php
// محاولة الحصول على الملاحظات بطرق متعددة
$notes_for_sheets = '';

// أولاً: محاولة الحصول على customer note
$customer_note = $order->get_customer_note();
if (!empty($customer_note)) {
    $notes_for_sheets = $customer_note;
} else {
    // ثانياً: محاولة الحصول من meta data (للطلبات القديمة)
    $meta_notes = get_post_meta($order_id, '_rid_cod_customer_notes', true);
    if (!empty($meta_notes)) {
        $notes_for_sheets = $meta_notes;
    } else {
        // أخيراً: محاولة الحصول من order notes
        $order_notes = $order->get_customer_order_notes();
        if (!empty($order_notes)) {
            $notes_for_sheets = $order_notes[0]->comment_content;
        }
    }
}
```

### 3. إصلاح حفظ الملاحظات في المسودات
أضيف معالجة الملاحظات في دالة `save_draft_order()`:

```php
// معالجة الملاحظات للمسودة
$customer_notes = '';
if (!empty($_POST['notes'])) {
    $customer_notes = sanitize_textarea_field($_POST['notes']);
} elseif (!empty($_POST['order_notes'])) {
    $customer_notes = sanitize_textarea_field($_POST['order_notes']);
}

// حفظ الملاحظات في المسودة
if (!empty(trim($customer_notes))) {
    $order->set_customer_note(trim($customer_notes));
}
```

### 4. إصلاح معالجة الولايات عند تفعيل النص
أضيف حفظ إضافي للولايات كـ meta data:

```php
// حفظ معلومات إضافية عن الولاية
$original_state_value = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';
$force_text_fields = get_option('rid_cod_force_text_fields', 'no') === 'yes';

// حفظ كـ meta data للأمان
update_post_meta($order_id, '_rid_cod_original_state_value', $original_state_value);
update_post_meta($order_id, '_rid_cod_state_full_name', $state_full_name);
update_post_meta($order_id, '_rid_cod_force_text_fields_used', $force_text_fields ? 'yes' : 'no');
```

### 5. تحسين استخراج الولايات لـ Google Sheets
أضيف آلية fallback متقدمة:

```php
// استخراج الولاية مع آلية fallback
$state_for_sheets = '';
$billing_state = $order->get_billing_state();

if (!empty($billing_state)) {
    $state_for_sheets = $billing_state;
} else {
    // محاولة الحصول من meta data
    $meta_state = get_post_meta($order_id, '_rid_cod_state_full_name', true);
    if (!empty($meta_state)) {
        $state_for_sheets = $meta_state;
    } else {
        // آخر محاولة: القيمة الأصلية
        $original_state = get_post_meta($order_id, '_rid_cod_original_state_value', true);
        if (!empty($original_state)) {
            $state_for_sheets = $original_state;
        }
    }
}
```

## ملف الاختبار
تم إنشاء ملف `test-notes-fix.php` لاختبار الإصلاح:
- يعرض الطلبات الحديثة وكيفية استخراج الملاحظات منها
- يمكن الوصول إليه من لوحة التحكم تحت قائمة RID COD

## النتائج المتوقعة
بعد هذه الإصلاحات:

### للملاحظات:
1. ✅ الملاحظات ستُحفظ بشكل صحيح في الطلبات الجديدة
2. ✅ الملاحظات ستُرسل إلى Google Sheets عند تفعيل الميزة
3. ✅ الطلبات القديمة ستعمل أيضاً (fallback mechanism)
4. ✅ المسودات ستحفظ الملاحظات أيضاً

### للولايات:
1. ✅ الولايات ستُرسل بشكل صحيح حتى عند تفعيل ميزة النص
2. ✅ البيانات ستُحفظ بطرق متعددة للأمان
3. ✅ آلية fallback متقدمة للطلبات القديمة
4. ✅ المسودات ستحفظ معلومات الولايات بشكل صحيح

## اختبار الإصلاحات
1. **اختبار الملاحظات**: قم بإنشاء طلب جديد مع ملاحظات وتحقق من وصولها لـ Google Sheets
2. **اختبار الولايات مع النص**: فعّل ميزة "تحويل الولايات والبلديات إلى حقول نصية" وأنشئ طلب جديد
3. **استخدام صفحة الاختبار**: اذهب إلى "RID COD Activation" > "اختبار الملاحظات" للتحقق من الطلبات الموجودة
4. **فحص Google Sheets**: تأكد من وصول البيانات بشكل صحيح
