<?php
/**
 * Plugin Name: RID COD Checkout
 * Plugin URI: https://r.ridcod.com/
 * Description: A custom checkout form for Cash on Delivery (COD) that displays directly on product pages. Requires activation.
 * Version: 3.0.7
 * Author: RID Code
 * Author URI: https://r.ridcod.com/
 * Text Domain: rid-cod
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 * WC requires at least: 4.0
 * WC tested up to: 7.0
 *
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin directory path constant
if (!defined('RID_COD_PLUGIN_DIR')) {
    define('RID_COD_PLUGIN_DIR', plugin_dir_path(__FILE__));
}

// Include the Country Manager
require_once RID_COD_PLUGIN_DIR . 'includes/countries/country-manager.php';

// Declare HPOS compatibility
add_action( 'before_woocommerce_init', function() {
 if ( class_exists( \Automattic\WooCommerce\Utilities\FeaturesUtil::class ) ) {
  \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
 }
} );

// --- License Constants ---
define('RID_COD_LICENSE_OPTION_KEY', 'rid_cod_license_key');
define('RID_COD_LICENSE_STATUS_OPTION', 'rid_cod_license_status');
define('RID_COD_ACTIVATION_PAGE_SLUG', 'rid-cod-activate');
define('RID_COD_LICENSE_SERVER_URL', 'https://r.ridcod.com/');
define('RID_COD_LAST_VALID_CHECK_OPTION', 'rid_cod_last_valid_check');
define('RID_COD_LICENSE_GRACE_PERIOD', 30 * DAY_IN_SECONDS); // 30 days grace period

// --- Helper function to get current domain ---
function rid_cod_get_current_domain() {
    return $_SERVER['HTTP_HOST'] ?? 'localhost';
}

// --- Helper function to get the API base URL ---
function rid_cod_get_api_base_url() {
    // Return the API URL from the license server
    return rtrim(RID_COD_LICENSE_SERVER_URL, '/') . '/api/';
}

// --- Helper function to check license status ---
function rid_cod_is_license_active() {
    // Check transient first for performance
    $status = get_transient('rid_cod_license_check');
    if (false !== $status) {
        return 'active' === $status;
    }

    // If transient expired or not set, verify with server
    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);
    if (empty($license_key)) {
        return false;
    }

    // Try to verify license with server
    $is_valid = rid_cod_verify_license_with_server($license_key);

    if ($is_valid) {
        // Server verification successful
        $status = 'active';
        update_option(RID_COD_LICENSE_STATUS_OPTION, $status);
        update_option(RID_COD_LAST_VALID_CHECK_OPTION, time()); // Save last successful check
        set_transient('rid_cod_license_check', $status, 24 * HOUR_IN_SECONDS); // Extended to 24 hours
        return true;
    } else {
        // Server verification failed - check if we're in grace period
        $last_valid_check = get_option(RID_COD_LAST_VALID_CHECK_OPTION, 0);
        $grace_period_end = $last_valid_check + RID_COD_LICENSE_GRACE_PERIOD;
        
        if (time() < $grace_period_end && $last_valid_check > 0) {
            // Still in grace period - keep license active
            error_log('RID COD: Server check failed, but still in grace period. Days remaining: ' . ceil(($grace_period_end - time()) / DAY_IN_SECONDS));
            set_transient('rid_cod_license_check', 'active', 6 * HOUR_IN_SECONDS); // Shorter cache during grace period
            return true;
        } else {
            // Grace period expired or never had a valid check
            $status = 'inactive';
            update_option(RID_COD_LICENSE_STATUS_OPTION, $status);
            set_transient('rid_cod_license_check', $status, 1 * HOUR_IN_SECONDS); // Short cache for failed checks
            return false;
        }
    }
}

// --- Helper function to decode and clean variation attributes ---
function rid_cod_decode_variation_attribute($value) {
    if (empty($value)) {
        return $value;
    }
    
    // Store original for comparison
    $original = $value;
    
    // First decode URL encoding (handles Arabic text from WooCommerce)
    $decoded = urldecode($value);
    
    // If still looks encoded, try additional decoding
    while (strpos($decoded, '%') !== false && $decoded !== urldecode($decoded)) {
        $decoded = urldecode($decoded);
    }
    
    // Additional cleaning for special characters
    $cleaned = trim($decoded);
    
    // Remove common prefixes if they exist
    $prefixes_to_remove = array('pa_', 'attribute_pa_', 'attribute_');
    foreach ($prefixes_to_remove as $prefix) {
        if (strpos($cleaned, $prefix) === 0) {
            $cleaned = substr($cleaned, strlen($prefix));
            break;
        }
    }
    
    // Final trim
    $cleaned = trim($cleaned);
    
    // Log the transformation for debugging (only if changed)
    if ($original !== $cleaned) {
        error_log("RID COD Variation Decode: '$original' -> '$cleaned'");
    }
    
    return $cleaned;
}

// --- Helper function to normalize variation attributes for comparison ---
function rid_cod_normalize_variation_attributes($attributes) {
    if (!is_array($attributes)) {
        return $attributes;
    }
    
    $normalized = array();
    foreach ($attributes as $key => $value) {
        // Clean the key (attribute name)
        $clean_key = rid_cod_decode_variation_attribute($key);
        // Clean the value
        $clean_value = rid_cod_decode_variation_attribute($value);
        $normalized[$clean_key] = $clean_value;
    }
    
    return $normalized;
}

// --- Helper function to verify license with server ---
function rid_cod_verify_license_with_server($license_key) {
    $api_url = rid_cod_get_api_base_url() . 'license-verify.php';
    $domain = rid_cod_get_current_domain();

    $response = wp_remote_post($api_url, array(
        'timeout' => 20, // Increased timeout
        'redirection' => 3,
        'user-agent' => 'RID-COD-Plugin/' . (defined('RID_COD_VERSION') ? RID_COD_VERSION : '1.5.0'),
        'body' => array(
            'license_key' => $license_key,
            'domain' => $domain,
            'action' => 'verify'
        ),
    ));

    if (is_wp_error($response)) {
        // Log the error for debugging
        error_log('RID COD: License verification failed - ' . $response->get_error_message());
        return false;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    
    // Handle non-200 responses
    if ($response_code !== 200) {
        error_log('RID COD: License verification returned HTTP ' . $response_code);
        return false;
    }
    
    $data = json_decode($body, true);
    
    // Handle JSON decode errors
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('RID COD: Invalid JSON response from license server');
        return false;
    }

    // Accept both 'active' and 'success' status
    if (isset($data['status'])) {
        $is_valid = ($data['status'] === 'active' || $data['status'] === 'success');
        if ($is_valid) {
            error_log('RID COD: License verification successful');
        } else {
            error_log('RID COD: License verification failed - Status: ' . $data['status']);
        }
        return $is_valid;
    }

    error_log('RID COD: License verification response missing status field');
    return false;
}

// --- Add Admin Menu for Activation ---
add_action('admin_menu', 'rid_cod_add_activation_menu');
function rid_cod_add_activation_menu() {
    add_menu_page(
        __('RID COD Activation', 'rid-cod'),
        __('RID COD Activation', 'rid-cod'),
        'manage_options',
        RID_COD_ACTIVATION_PAGE_SLUG,
        'rid_cod_render_activation_page',
        'dashicons-lock', // Icon
        81 // Position slightly below WooCommerce
    );
}

// --- Render Activation Page ---
function rid_cod_render_activation_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html__('RID COD License Activation', 'rid-cod'); ?></h1>
        <?php
        // Handle form submission for activation
        if (isset($_POST['rid_cod_activate_nonce']) && wp_verify_nonce($_POST['rid_cod_activate_nonce'], 'rid_cod_activate_action')) {
            if (isset($_POST['activate_license'])) {
                 rid_cod_handle_license_activation();
            }
        }

        $license_key = get_option(RID_COD_LICENSE_OPTION_KEY, '');
        $status = get_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive');
        $last_valid_check = get_option(RID_COD_LAST_VALID_CHECK_OPTION, 0);
        ?>

         <p><?php esc_html_e('Please enter your license key to activate the plugin.', 'rid-cod'); ?></p>

        <form method="post" action="">
            <?php wp_nonce_field('rid_cod_activate_action', 'rid_cod_activate_nonce'); ?>
            <table class="form-table">
                <tbody>
                    <tr>
                        <th scope="row"><label for="rid_cod_license_key"><?php esc_html_e('License Key', 'rid-cod'); ?></label></th>
                        <td><input name="rid_cod_license_key" type="text" id="rid_cod_license_key" value="<?php echo esc_attr($license_key); ?>" class="regular-text" /></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php esc_html_e('License Status', 'rid-cod'); ?></th>
                        <td>
                            <?php if ('active' === $status) : ?>
                                <span style="color: green; font-weight: bold;"><?php esc_html_e('Active', 'rid-cod'); ?></span>
                                <p class="description"><?php esc_html_e('Your license is active and the plugin is working.', 'rid-cod'); ?></p>
                                <?php if ($last_valid_check > 0) : ?>
                                    <p class="description">
                                        <?php 
                                        /* translators: %s: formatted date */
                                        printf(esc_html__('Last verified: %s', 'rid-cod'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $last_valid_check)); 
                                        ?>
                                    </p>
                                <?php endif; ?>
                            <?php else : ?>
                                <span style="color: red; font-weight: bold;"><?php esc_html_e('Inactive', 'rid-cod'); ?></span>
                                <p class="description"><?php esc_html_e('Please enter your license key and click Activate.', 'rid-cod'); ?></p>
                                <?php 
                                // Check if we're in grace period
                                if ($last_valid_check > 0) {
                                    $grace_period_end = $last_valid_check + RID_COD_LICENSE_GRACE_PERIOD;
                                    if (time() < $grace_period_end) {
                                        $days_remaining = ceil(($grace_period_end - time()) / DAY_IN_SECONDS);
                                        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin-top: 10px; border-radius: 4px;">';
                                        echo '<strong>' . esc_html__('Grace Period Active', 'rid-cod') . '</strong><br>';
                                        /* translators: %d: number of days */
                                        printf(esc_html__('Plugin will work for %d more days while we try to reconnect to the license server.', 'rid-cod'), $days_remaining);
                                        echo '</div>';
                                    }
                                }
                                ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php esc_html_e('Current Domain', 'rid-cod'); ?></th>
                        <td>
                            <code><?php echo esc_html(rid_cod_get_current_domain()); ?></code>
                            <p class="description"><?php esc_html_e('This is the domain that will be registered with your license.', 'rid-cod'); ?></p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <?php if ('active' === $status) : ?>
                <?php submit_button(__('Update License', 'rid-cod'), 'primary', 'activate_license'); ?>
            <?php else : ?>
                <?php submit_button(__('Activate License', 'rid-cod'), 'primary', 'activate_license'); ?>
            <?php endif; ?>
        </form>

        <?php if ('active' === $status && !empty($license_key)) : ?>
            <hr>
            <h2><?php esc_html_e('Deactivate License', 'rid-cod'); ?></h2>
            <p><?php esc_html_e('If you want to use this license on a different domain, you can deactivate it here first.', 'rid-cod'); ?></p>
            <form method="post" action="" onsubmit="return confirm('<?php esc_attr_e('Are you sure you want to deactivate this license?', 'rid-cod'); ?>');">
                <?php wp_nonce_field('rid_cod_deactivate_action', 'rid_cod_deactivate_nonce'); ?>
                <?php submit_button(__('Deactivate License', 'rid-cod'), 'delete', 'deactivate_license'); ?>
            </form>
        <?php endif; ?>
    </div>
    <?php
}

// --- Handle Activation Logic ---
function rid_cod_handle_license_activation() {
    $license_key = isset($_POST['rid_cod_license_key']) ? sanitize_text_field($_POST['rid_cod_license_key']) : '';
    $domain = rid_cod_get_current_domain();
    $api_url = rid_cod_get_api_base_url() . 'license-verify.php';

    if (empty($license_key)) {
        add_settings_error('rid_cod_messages', 'empty_key', __('Please enter a license key.', 'rid-cod'), 'error');
        settings_errors('rid_cod_messages');
        return;
    }

    $response = wp_remote_post($api_url, array(
        'timeout' => 15,
        'body' => array(
            'license_key' => $license_key,
            'domain' => $domain,
            'action' => 'activate'
        ),
    ));

    if (is_wp_error($response)) {
        add_settings_error('rid_cod_messages', 'api_error', __('Error connecting to activation server:', 'rid-cod') . ' ' . $response->get_error_message(), 'error');
    } else {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        $response_code = wp_remote_retrieve_response_code($response);

        // Debug logging
        error_log('RID COD: Response code: ' . $response_code);
        error_log('RID COD: Response body: ' . $body);

        // Check for successful activation
        $is_success = false;
        if ($response_code === 200 && isset($data['status'])) {
            // Accept both 'active' and 'success' status
            if ($data['status'] === 'active' || $data['status'] === 'success') {
                $is_success = true;
            }
        }

        if ($is_success) {
            update_option(RID_COD_LICENSE_OPTION_KEY, $license_key);
            update_option(RID_COD_LICENSE_STATUS_OPTION, 'active');
            update_option(RID_COD_LAST_VALID_CHECK_OPTION, time()); // Save successful activation time
            delete_transient('rid_cod_license_check'); // Clear transient cache

            // Use the server message if available, otherwise default message
            $success_message = isset($data['message']) ? $data['message'] : __('License activated successfully!', 'rid-cod');
            add_settings_error('rid_cod_messages', 'activation_success', $success_message, 'updated');
        } else {
            // Activation failed or API returned error
            $message = isset($data['message']) ? $data['message'] : __('License verification failed.', 'rid-cod');
             // Append HTTP status code if not 200 for more context
            if ($response_code !== 200) {
                 $message .= ' (HTTP ' . $response_code . ')';
            }
            // Add status info for debugging
            if (isset($data['status'])) {
                $message .= ' (Status: ' . $data['status'] . ')';
            }

            update_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive'); // Ensure status is inactive
            delete_transient('rid_cod_license_check'); // Clear transient cache
            add_settings_error('rid_cod_messages', 'activation_failed', __('License activation failed:', 'rid-cod') . ' ' . esc_html($message), 'error');
        }
    }
    // Display messages
    settings_errors('rid_cod_messages');
}

// --- Conditional Loading based on License ---

// Check if WooCommerce is activated (needs to happen early)
include_once(ABSPATH . 'wp-admin/includes/plugin.php');
if (!is_plugin_active('woocommerce/woocommerce.php')) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>' . __('RID COD Checkout requires WooCommerce to be installed and activated.', 'rid-cod') . '</p></div>';
    });
    // Don't return here yet, allow activation page to load
}

// Only load the core plugin if the license is active
if (rid_cod_is_license_active()) {

    // Define plugin constants (only if active and not already defined)
    if (!defined('RID_COD_PLUGIN_URL')) {
        define('RID_COD_PLUGIN_URL', plugin_dir_url(__FILE__));
    }
    if (!defined('RID_COD_VERSION')) {
        define('RID_COD_VERSION', '3.0.8');
    }

    // Include core files (only if active)
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-loader.php';
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-form.php';
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-ajax.php';
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-customizer.php';
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-product-meta.php';
    // Include other necessary files if they exist
    if (file_exists(RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-admin-settings.php')) {
        require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-admin-settings.php';
    }
     if (file_exists(RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-google-sheets-settings.php')) {
        require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-google-sheets-settings.php';
    }
    // Include product price modifier class
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-product-price.php';

    // Include order limiter class for 24-hour order prevention
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-order-limiter.php';

    // Include migration script
    require_once RID_COD_PLUGIN_DIR . 'includes/migration.php';

    // Include shipping manager
    require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-shipping-manager.php';

    // Initialize the plugin (only if active)
    function rid_cod_init() {
        // Load plugin text domain
        load_plugin_textdomain('rid-cod', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Initialize the plugin components
        $loader = new RID_COD_Loader();
        $loader->init();

        // Initialize other components if they exist and need init
         if (class_exists('RID_COD_Google_Sheets_Settings')) {
            new RID_COD_Google_Sheets_Settings();
        }
    }
    add_action('plugins_loaded', 'rid_cod_init', 11); // Use priority 11

} else {
    // Optional: Add an admin notice if WooCommerce is active but license is not
    if (is_plugin_active('woocommerce/woocommerce.php')) {
        add_action('admin_notices', 'rid_cod_show_activation_notice');
    }
}

function rid_cod_show_activation_notice() {
    // Only show if not on the activation page itself
    $screen = get_current_screen();
    if ($screen && 'toplevel_page_' . RID_COD_ACTIVATION_PAGE_SLUG !== $screen->id) {
        $activation_url = admin_url('admin.php?page=' . RID_COD_ACTIVATION_PAGE_SLUG);
        $license_info = rid_cod_get_license_status_info();
        
        if ($license_info['in_grace_period']) {
            // Show different message during grace period
            echo '<div class="notice notice-warning is-dismissible"><p>' .
                 sprintf(
                     /* translators: 1: number of days, 2: activation page URL */
                     __('RID COD Checkout: Unable to verify license with server. Plugin will work for %1$d more days. Please check your <a href="%2$s">license settings</a> if the issue persists.', 'rid-cod'),
                     $license_info['grace_days_remaining'],
                     esc_url($activation_url)
                 ) .
                 '</p></div>';
        } else {
            // Standard activation message
            echo '<div class="notice notice-warning is-dismissible"><p>' .
                 sprintf(
                     __('RID COD Checkout is installed but requires activation. Please <a href="%s">enter your license key</a> to enable its features.', 'rid-cod'),
                     esc_url($activation_url)
                 ) .
                 '</p></div>';
        }
    }
}

// --- Activation/Deactivation Hooks ---

// Activation hook - Schedule weekly check
register_activation_hook(__FILE__, 'rid_cod_plugin_activate');
function rid_cod_plugin_activate() {
    // Schedule the cron job if it doesn't exist
    if (!wp_next_scheduled('rid_cod_weekly_license_check')) {
        wp_schedule_event(time(), 'weekly', 'rid_cod_weekly_license_check');
    }
    // Schedule daily cleanup for order limiter records
    if (!wp_next_scheduled('rid_cod_daily_cleanup_order_limits')) {
        wp_schedule_event(time(), 'daily', 'rid_cod_daily_cleanup_order_limits');
    }
    // Set default status if not set
    if (false === get_option(RID_COD_LICENSE_STATUS_OPTION)) {
        update_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive');
    }
    // Remove old server URL option if it exists
    delete_option('rid_cod_license_server_url');
    // Clear any old license cache
    delete_transient('rid_cod_license_check');
    
    // Initialize last valid check if not exists
    if (false === get_option(RID_COD_LAST_VALID_CHECK_OPTION)) {
        update_option(RID_COD_LAST_VALID_CHECK_OPTION, 0);
    }
}

// Deactivation hook - Clear scheduled check
register_deactivation_hook(__FILE__, 'rid_cod_plugin_deactivate');
function rid_cod_plugin_deactivate() {
    // Clear the scheduled cron jobs
    wp_clear_scheduled_hook('rid_cod_weekly_license_check');
    wp_clear_scheduled_hook('rid_cod_daily_cleanup_order_limits');
}

// --- Cron Job for Weekly Check ---
add_action('rid_cod_weekly_license_check', 'rid_cod_perform_weekly_check');
function rid_cod_perform_weekly_check() {
    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);
    $current_status = get_option(RID_COD_LICENSE_STATUS_OPTION);

    // Only check if there's a key and the status is currently considered active
    if (empty($license_key) || 'active' !== $current_status) {
        return; // No key or already inactive, nothing to check
    }

    // Use the updated verification function
    $is_valid = rid_cod_verify_license_with_server($license_key);

    if ($is_valid) {
        // License is still valid, update last valid check time
        update_option(RID_COD_LAST_VALID_CHECK_OPTION, time());
        set_transient('rid_cod_license_check', 'active', 24 * HOUR_IN_SECONDS);
        error_log('RID COD: Weekly license check successful');
    } else {
        // Server check failed - let the grace period system handle it
        delete_transient('rid_cod_license_check'); // Force re-check on next request
        error_log('RID COD: Weekly license check failed - grace period system will handle');
    }
}

// --- Cron Job for Daily Order Limits Cleanup ---
add_action('rid_cod_daily_cleanup_order_limits', 'rid_cod_perform_daily_cleanup');
function rid_cod_perform_daily_cleanup() {
    // Only run cleanup if the order limiter feature is enabled
    $prevent_24h_orders = get_option('rid_cod_prevent_2nd_order_24h', 'no') === 'yes';

    if ($prevent_24h_orders && class_exists('RID_COD_Order_Limiter')) {
        RID_COD_Order_Limiter::cleanup_expired_records();
    }
}

// --- Update Checker System ---
add_filter('pre_set_site_transient_update_plugins', 'rid_cod_check_for_updates');
function rid_cod_check_for_updates($transient) {
    if (empty($transient->checked)) {
        return $transient;
    }

    $plugin_slug = plugin_basename(__FILE__);
    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);

    if (empty($license_key) || !rid_cod_is_license_active()) {
        return $transient;
    }

    $api_url = rid_cod_get_api_base_url() . 'update-check.php';
    $domain = rid_cod_get_current_domain();

    $response = wp_remote_post($api_url, array(
        'timeout' => 15,
        'body' => array(
            'license_key' => $license_key,
            'domain' => $domain,
            'plugin_version' => RID_COD_VERSION,
            'plugin_slug' => $plugin_slug
        ),
    ));

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['new_version']) && version_compare(RID_COD_VERSION, $data['new_version'], '<')) {
            $transient->response[$plugin_slug] = (object) array(
                'slug' => dirname($plugin_slug),
                'plugin' => $plugin_slug,
                'new_version' => $data['new_version'],
                'url' => isset($data['url']) ? $data['url'] : '',
                'package' => isset($data['package']) ? $data['package'] : '',
                'tested' => isset($data['tested']) ? $data['tested'] : '',
                'requires_php' => isset($data['requires_php']) ? $data['requires_php'] : '',
                'compatibility' => new stdClass(),
            );
        }
    }

    return $transient;
}

// --- Plugin Information for Updates ---
add_filter('plugins_api', 'rid_cod_plugin_info', 20, 3);
function rid_cod_plugin_info($result, $action, $args) {
    if ($action !== 'plugin_information') {
        return $result;
    }

    $plugin_slug = dirname(plugin_basename(__FILE__));

    if ($args->slug !== $plugin_slug) {
        return $result;
    }

    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);

    if (empty($license_key) || !rid_cod_is_license_active()) {
        return $result;
    }

    $api_url = rid_cod_get_api_base_url() . 'update-check.php';
    $domain = rid_cod_get_current_domain();

    $response = wp_remote_post($api_url, array(
        'timeout' => 15,
        'body' => array(
            'license_key' => $license_key,
            'domain' => $domain,
            'plugin_version' => RID_COD_VERSION,
            'plugin_slug' => plugin_basename(__FILE__),
            'action' => 'info'
        ),
    ));

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['name'])) {
            return (object) array(
                'name' => isset($data['name']) ? $data['name'] : 'RID COD Checkout',
                'slug' => $plugin_slug,
                'version' => isset($data['version']) ? $data['version'] : RID_COD_VERSION,
                'author' => isset($data['author']) ? $data['author'] : 'RID Code',
                'homepage' => isset($data['homepage']) ? $data['homepage'] : '',
                'short_description' => isset($data['description']) ? $data['description'] : '',
                'sections' => array(
                    'description' => isset($data['description']) ? $data['description'] : '',
                    'changelog' => isset($data['changelog']) ? $data['changelog'] : '',
                ),
                'download_link' => isset($data['package']) ? $data['package'] : '',
                'tested' => isset($data['tested']) ? $data['tested'] : '',
                'requires_php' => isset($data['requires_php']) ? $data['requires_php'] : '',
                'last_updated' => isset($data['last_updated']) ? $data['last_updated'] : '',
            );
        }
    }

    return $result;
}

// --- Download Handler for Updates ---
add_filter('upgrader_pre_download', 'rid_cod_upgrader_pre_download', 10, 3);
function rid_cod_upgrader_pre_download($reply, $package, $upgrader) {
    if (strpos($package, rid_cod_get_api_base_url()) !== false) {
        $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);
        $domain = rid_cod_get_current_domain();

        if (empty($license_key)) {
            return new WP_Error('no_license', __('License key is required for updates.', 'rid-cod'));
        }

        $download_url = rid_cod_get_api_base_url() . 'download.php';

        $args = array(
            'timeout' => 300,
            'body' => array(
                'license_key' => $license_key,
                'domain' => $domain,
                'item_name' => 'RID COD Checkout'
            )
        );

        $response = wp_remote_post($download_url, $args);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            return new WP_Error('download_failed', __('Failed to download update.', 'rid-cod'));
        }

        $body = wp_remote_retrieve_body($response);

        // Check if response is JSON (error) or ZIP file
        $json_data = json_decode($body, true);
        if ($json_data && isset($json_data['error'])) {
            return new WP_Error('download_error', $json_data['message'] ?? __('Download failed.', 'rid-cod'));
        }

        // Save the ZIP file temporarily
        $temp_file = download_url($package);
        if (is_wp_error($temp_file)) {
            // If direct download fails, save the body to a temp file
            $temp_file = wp_tempnam();
            file_put_contents($temp_file, $body);
        }

        return $temp_file;
    }

    return $reply;
}

// --- Add license deactivation function ---
function rid_cod_deactivate_license() {
    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);

    if (empty($license_key)) {
        return false;
    }

    $api_url = rid_cod_get_api_base_url() . 'license-verify.php';
    $domain = rid_cod_get_current_domain();

    $response = wp_remote_post($api_url, array(
        'timeout' => 15,
        'body' => array(
            'license_key' => $license_key,
            'domain' => $domain,
            'action' => 'deactivate'
        ),
    ));

    // Update local status regardless of server response
    update_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive');
    update_option(RID_COD_LAST_VALID_CHECK_OPTION, 0); // Clear last valid check
    delete_transient('rid_cod_license_check');

    return !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;
}

// --- Add deactivate button to activation page ---
add_action('admin_init', 'rid_cod_handle_deactivation');
function rid_cod_handle_deactivation() {
    if (isset($_POST['rid_cod_deactivate_nonce']) && wp_verify_nonce($_POST['rid_cod_deactivate_nonce'], 'rid_cod_deactivate_action')) {
        if (isset($_POST['deactivate_license'])) {
            if (rid_cod_deactivate_license()) {
                add_settings_error('rid_cod_messages', 'deactivation_success', __('License deactivated successfully!', 'rid-cod'), 'updated');
            } else {
                add_settings_error('rid_cod_messages', 'deactivation_failed', __('License deactivation failed, but local status has been cleared.', 'rid-cod'), 'error');
            }
            settings_errors('rid_cod_messages');
        }
    }
}

// --- Helper function to get license status information ---
function rid_cod_get_license_status_info() {
    $license_key = get_option(RID_COD_LICENSE_OPTION_KEY);
    $status = get_option(RID_COD_LICENSE_STATUS_OPTION, 'inactive');
    $last_valid_check = get_option(RID_COD_LAST_VALID_CHECK_OPTION, 0);
    
    $info = array(
        'status' => $status,
        'has_key' => !empty($license_key),
        'last_check' => $last_valid_check,
        'in_grace_period' => false,
        'grace_days_remaining' => 0
    );
    
    // Check grace period status
    if ($last_valid_check > 0) {
        $grace_period_end = $last_valid_check + RID_COD_LICENSE_GRACE_PERIOD;
        if (time() < $grace_period_end) {
            $info['in_grace_period'] = true;
            $info['grace_days_remaining'] = ceil(($grace_period_end - time()) / DAY_IN_SECONDS);
        }
    }
    
    return $info;
}
