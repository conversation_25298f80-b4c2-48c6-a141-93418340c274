<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النموذج على الهاتف المحمول</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            overflow-x: hidden; /* منع التمرير الأفقي */
        }
        .test-container {
            background-color: #fff;
            margin: 20px 0;
        }
        .test-header {
            background-color: #333;
            color: white;
            padding: 10px;
            text-align: center;
        }
        .viewport-info {
            position: fixed;
            top: 0;
            left: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <div class="viewport-info" id="viewport-info">
        العرض: <span id="width"></span>px
    </div>
    
    <div class="test-header">
        <h2>اختبار النموذج الحديث على الهاتف المحمول</h2>
        <p style="font-size: 14px; margin: 5px 0;">يجب أن يكون النموذج بعرض كامل بدون فراغات جانبية وبدون تحريك الصفحة</p>
    </div>
    
    <div class="test-container">
        <div class="rid-cod-form-modern">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>اطلب الآن - الدفع عند الاستلام</h3>
                </div>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="الاسم الكامل" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="العنوان" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <select required>
                            <option value="">اختر المحافظة</option>
                            <option value="baghdad">بغداد</option>
                            <option value="basra">البصرة</option>
                            <option value="erbil">أربيل</option>
                        </select>
                    </div>
                </div>
                
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-quantity">
                        <label>الكمية:</label>
                        <input type="number" value="1" min="1">
                    </div>
                    <div class="rid-cod-submit">
                        <button id="rid-cod-submit-btn" type="submit">
                            اطلب الآن - الدفع عند الاستلام
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-header">
        <h2>اختبار النموذج الكلاسيكي</h2>
    </div>
    
    <div class="test-container">
        <div class="rid-cod-form-classic">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>اطلب الآن - الدفع عند الاستلام</h3>
                </div>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="الاسم الكامل" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                </div>
                
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button id="rid-cod-submit-btn" type="submit">
                            اطلب الآن
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            document.getElementById('width').textContent = window.innerWidth;
        }
        
        window.addEventListener('resize', updateViewportInfo);
        updateViewportInfo();
    </script>
</body>
</html>
