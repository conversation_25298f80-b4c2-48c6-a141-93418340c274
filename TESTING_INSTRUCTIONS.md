# تعليمات اختبار الإصلاحات

## خطوات الاختبار السريع

### 1. اختبار الملاحظات
1. اذهب إلى أي منتج في الموقع
2. املأ النموذج وأضف ملاحظات في حقل "ملاحظات إضافية"
3. <PERSON><PERSON><PERSON>ل الطلب
4. تحقق من Google Sheets - يجب أن تظهر الملاحظات في عمود "الملاحظات"

### 2. اختبار الولايات مع النص
1. اذهب إلى **لوحة التحكم** > **RID COD Activation** > **إعدادات النموذج**
2. فعّل خيار "تحويل الولايات والبلديات إلى حقول نصية"
3. احفظ الإعدادات
4. اذهب إلى أي منتج في الموقع
5. املأ النموذج (لاحظ أن حقل الولاية أصبح نصياً)
6. اكتب اسم الولاية يدوياً (مثل: "الجزائر العاصمة")
7. أرسل الطلب
8. تحقق من Google Sheets - يجب أن تظهر الولاية في عمود "الولاية"

### 3. استخدام صفحة الاختبار
1. اذهب إلى **لوحة التحكم** > **RID COD Activation** > **اختبار الملاحظات**
2. ستظهر جداول تحليلية للطلبات الحديثة
3. تحقق من:
   - **جدول الملاحظات**: العمود الأخير يجب أن يكون أخضر إذا كانت الملاحظات موجودة
   - **جدول الولايات**: العمود الأخير يجب أن يكون أخضر إذا كانت الولايات موجودة

## علامات النجاح

### ✅ الملاحظات تعمل بشكل صحيح إذا:
- ظهرت الملاحظات في Google Sheets
- العمود الأخير في جدول الاختبار أخضر
- الملاحظات محفوظة في تفاصيل الطلب في WooCommerce

### ✅ الولايات تعمل بشكل صحيح إذا:
- ظهرت الولايات في Google Sheets حتى مع تفعيل النص
- العمود الأخير في جدول اختبار الولايات أخضر
- الولايات محفوظة في تفاصيل الطلب في WooCommerce

## في حالة وجود مشاكل

### إذا لم تظهر الملاحظات:
1. تحقق من تفعيل ميزة Google Sheets في الإعدادات
2. تحقق من صحة رابط Google Apps Script
3. راجع سجل الأخطاء في WordPress (wp-content/debug.log)

### إذا لم تظهر الولايات:
1. تحقق من أن الولاية تم إدخالها في النموذج
2. تحقق من إعدادات "إظهار الولايات" في إعدادات النموذج
3. راجع سجل الأخطاء في WordPress

## ملاحظات مهمة
- الإصلاحات تعمل للطلبات الجديدة والقديمة
- المسودات أيضاً تحفظ البيانات بشكل صحيح
- يمكن التبديل بين وضع القوائم المنسدلة والنص دون مشاكل
- البيانات محفوظة بطرق متعددة للأمان
