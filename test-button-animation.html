<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أنيميشن زر تأكيد الطلب</title>
    <link rel="stylesheet" href="assets/css/rid-cod.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            overflow-x: hidden;
        }
        .test-container {
            background-color: #fff;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            background-color: #333;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .animation-toggle {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        .toggle-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
        }
        .toggle-btn:hover {
            background: #1976d2;
        }
        .toggle-btn.active {
            background: #4caf50;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>اختبار أنيميشن زر تأكيد الطلب لجميع النماذج</h1>
        <p>يمكنك تفعيل/إلغاء الأنيميشن لمعاينة الفرق</p>
    </div>
    
    <div class="animation-toggle">
        <h3>التحكم في الأنيميشن:</h3>
        <button class="toggle-btn active" onclick="toggleAnimation(true)">تفعيل الأنيميشن</button>
        <button class="toggle-btn" onclick="toggleAnimation(false)">إلغاء الأنيميشن</button>
        <div class="debug-info" id="debug-info">
            حالة الأنيميشن: مفعل | الكلاسات المطبقة: animate-button
        </div>
    </div>
    
    <!-- النموذج الكلاسيكي -->
    <div class="test-container">
        <h2>النموذج الكلاسيكي (Classic Form)</h2>
        <div class="rid-cod-form-classic animate-button" id="classic-form">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>اطلب الآن - الدفع عند الاستلام</h3>
                </div>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="الاسم الكامل" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                </div>
                
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button id="rid-cod-submit-btn" type="submit">
                            <span>اطلب الآن - الدفع عند الاستلام</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النموذج الحديث -->
    <div class="test-container">
        <h2>النموذج الحديث (Modern Form)</h2>
        <div class="rid-cod-form-modern animate-button" id="modern-form">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>اطلب الآن - الدفع عند الاستلام</h3>
                </div>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="الاسم الكامل" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                </div>
                
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button id="rid-cod-submit-btn" type="submit">
                            <span>اطلب الآن - الدفع عند الاستلام</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النموذج الثاني -->
    <div class="test-container">
        <h2>النموذج الثاني (Second Form)</h2>
        <div class="rid-cod-form-second animate-button" id="second-form">
            <div id="rid-cod-checkout">
                <div class="rid-cod-title">
                    <h3>اطلب الآن - الدفع عند الاستلام</h3>
                </div>
                
                <div class="rid-cod-customer-info">
                    <div class="rid-cod-field-group">
                        <input type="text" placeholder="الاسم الكامل" required>
                    </div>
                    <div class="rid-cod-field-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                </div>
                
                <div class="rid-cod-actions-row">
                    <div class="rid-cod-submit">
                        <button id="rid-cod-submit-btn" type="submit">
                            <span>اطلب الآن - الدفع عند الاستلام</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleAnimation(enable) {
            const forms = document.querySelectorAll('.rid-cod-form-classic, .rid-cod-form-modern, .rid-cod-form-second');
            const buttons = document.querySelectorAll('.toggle-btn');
            const debugInfo = document.getElementById('debug-info');

            forms.forEach(form => {
                if (enable) {
                    form.classList.add('animate-button');
                } else {
                    form.classList.remove('animate-button');
                }
            });

            // Update button states
            buttons.forEach((btn, index) => {
                if ((enable && index === 0) || (!enable && index === 1)) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });

            // Update debug info
            const status = enable ? 'مفعل' : 'معطل';
            const classes = enable ? 'animate-button' : 'بدون كلاس أنيميشن';
            debugInfo.textContent = `حالة الأنيميشن: ${status} | الكلاسات المطبقة: ${classes}`;

            // Log for debugging
            console.log('Animation toggled:', enable);
            forms.forEach((form, index) => {
                console.log(`Form ${index + 1} classes:`, form.className);
            });
        }
        
        // Add click handlers to buttons for demo
        document.querySelectorAll('#rid-cod-submit-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('تم النقر على زر: ' + this.textContent.trim());
            });
        });
    </script>
</body>
</html>
