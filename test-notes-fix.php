<?php
/**
 * Test file to verify notes fix for Google Sheets
 * This file can be used to test if notes are properly saved and retrieved
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Function to test notes handling
function rid_cod_test_notes_handling() {
    // This function can be called from WordPress admin or via AJAX
    // to test if notes are properly handled

    echo "<h3>اختبار معالجة الملاحظات</h3>";

    // Get recent orders to test
    $orders = wc_get_orders(array(
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_key' => '_payment_method',
        'meta_value' => 'cod'
    ));

    if (empty($orders)) {
        echo "<p>لا توجد طلبات للاختبار</p>";
        return;
    }

    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>Customer Note</th><th>Meta Notes</th><th>Order Notes</th><th>Notes for Sheets</th></tr>";

    foreach ($orders as $order) {
        $order_id = $order->get_id();

        // Get customer note
        $customer_note = $order->get_customer_note();

        // Get meta notes
        $meta_notes = get_post_meta($order_id, '_rid_cod_customer_notes', true);

        // Get order notes
        $order_notes = $order->get_customer_order_notes();
        $first_order_note = !empty($order_notes) ? $order_notes[0]->comment_content : '';

        // Simulate the Google Sheets logic
        $notes_for_sheets = '';
        if (!empty($customer_note)) {
            $notes_for_sheets = $customer_note;
        } else {
            if (!empty($meta_notes)) {
                $notes_for_sheets = $meta_notes;
            } else {
                if (!empty($order_notes)) {
                    $notes_for_sheets = $order_notes[0]->comment_content;
                }
            }
        }

        echo "<tr>";
        echo "<td>" . $order_id . "</td>";
        echo "<td>" . esc_html($customer_note) . "</td>";
        echo "<td>" . esc_html($meta_notes) . "</td>";
        echo "<td>" . esc_html($first_order_note) . "</td>";
        echo "<td style='background-color: " . (!empty($notes_for_sheets) ? '#d4edda' : '#f8d7da') . "'>" . esc_html($notes_for_sheets) . "</td>";
        echo "</tr>";
    }

    echo "</table>";

    echo "<p><strong>ملاحظة:</strong> العمود الأخير (Notes for Sheets) يجب أن يحتوي على الملاحظات إذا كانت موجودة. اللون الأخضر يعني نجح الاستخراج، والأحمر يعني فشل.</p>";
}

// Function to test state handling
function rid_cod_test_state_handling() {
    echo "<h3>اختبار معالجة الولايات</h3>";

    // Get recent orders to test
    $orders = wc_get_orders(array(
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_key' => '_payment_method',
        'meta_value' => 'cod'
    ));

    if (empty($orders)) {
        echo "<p>لا توجد طلبات للاختبار</p>";
        return;
    }

    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>رقم الطلب</th><th>Billing State</th><th>Meta State Full Name</th><th>Meta Original State</th><th>Text Fields Used</th><th>State for Sheets</th></tr>";

    foreach ($orders as $order) {
        $order_id = $order->get_id();

        // Get billing state
        $billing_state = $order->get_billing_state();

        // Get meta data
        $meta_state_full_name = get_post_meta($order_id, '_rid_cod_state_full_name', true);
        $meta_original_state = get_post_meta($order_id, '_rid_cod_original_state_value', true);
        $text_fields_used = get_post_meta($order_id, '_rid_cod_force_text_fields_used', true);

        // Simulate the Google Sheets logic
        $state_for_sheets = '';
        if (!empty($billing_state)) {
            $state_for_sheets = $billing_state;
        } else {
            if (!empty($meta_state_full_name)) {
                $state_for_sheets = $meta_state_full_name;
            } else {
                if (!empty($meta_original_state)) {
                    $state_for_sheets = $meta_original_state;
                }
            }
        }

        echo "<tr>";
        echo "<td>" . $order_id . "</td>";
        echo "<td>" . esc_html($billing_state) . "</td>";
        echo "<td>" . esc_html($meta_state_full_name) . "</td>";
        echo "<td>" . esc_html($meta_original_state) . "</td>";
        echo "<td>" . esc_html($text_fields_used) . "</td>";
        echo "<td style='background-color: " . (!empty($state_for_sheets) ? '#d4edda' : '#f8d7da') . "'>" . esc_html($state_for_sheets) . "</td>";
        echo "</tr>";
    }

    echo "</table>";

    echo "<p><strong>ملاحظة:</strong> العمود الأخير (State for Sheets) يجب أن يحتوي على الولاية. اللون الأخضر يعني نجح الاستخراج، والأحمر يعني فشل.</p>";
}

// Add admin page for testing (only for admins)
if (is_admin()) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'rid-cod-activate',
            'اختبار الملاحظات',
            'اختبار الملاحظات',
            'manage_options',
            'rid-cod-test-notes',
            'rid_cod_test_notes_page'
        );
    });
    
    function rid_cod_test_notes_page() {
        echo '<div class="wrap">';
        echo '<h1>اختبار معالجة البيانات - RID COD</h1>';
        echo '<p>هذه الصفحة تساعد في اختبار ما إذا كانت الملاحظات والولايات يتم حفظها واستخراجها بشكل صحيح لإرسالها إلى Google Sheets.</p>';

        if (function_exists('rid_cod_test_notes_handling')) {
            rid_cod_test_notes_handling();
            echo '<hr>';
            rid_cod_test_state_handling();
        } else {
            echo '<p>خطأ: لا يمكن العثور على دالة الاختبار</p>';
        }

        echo '</div>';
    }
}
