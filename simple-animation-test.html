<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للأنيميشن</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #rid-cod-submit-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        
        /* Animation CSS */
        .animate-button #rid-cod-submit-btn {
            animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite !important;
            transform: translate3d(0, 0, 0) !important;
        }
        
        @keyframes shaking {
            0%, 100% {
                transform: translate3d(0, 0, 0);
            }
            4%, 46% {
                transform: translate3d(-1px, 0, 0);
            }
            8%, 42% {
                transform: translate3d(2px, 0, 0);
            }
            12%, 37% {
                transform: translate3d(-3px, 0, 0);
            }
            16%, 33% {
                transform: translate3d(3px, 0, 0);
            }
        }
        
        .toggle-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .status {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>اختبار بسيط لأنيميشن الزر</h1>
    
    <div class="test-container">
        <h2>التحكم:</h2>
        <button class="toggle-btn" onclick="enableAnimation()">تفعيل الأنيميشن</button>
        <button class="toggle-btn" onclick="disableAnimation()">إلغاء الأنيميشن</button>
        
        <div class="status" id="status">
            الحالة: غير محدد
        </div>
    </div>
    
    <div class="test-container" id="form-container">
        <h2>النموذج:</h2>
        <button id="rid-cod-submit-btn">اطلب الآن - الدفع عند الاستلام</button>
        
        <p><strong>الكلاسات الحالية:</strong> <span id="current-classes">لا يوجد</span></p>
    </div>
    
    <div class="test-container">
        <h2>معلومات تشخيصية:</h2>
        <ul>
            <li>إذا كان الزر يهتز، فالأنيميشن يعمل</li>
            <li>إذا لم يهتز، فهناك مشكلة في CSS أو JavaScript</li>
            <li>تحقق من وحدة تحكم المطور (F12) للأخطاء</li>
        </ul>
    </div>

    <script>
        function updateStatus() {
            const container = document.getElementById('form-container');
            const status = document.getElementById('status');
            const classesSpan = document.getElementById('current-classes');
            
            const hasAnimateClass = container.classList.contains('animate-button');
            status.textContent = hasAnimateClass ? 'الحالة: الأنيميشن مفعل' : 'الحالة: الأنيميشن معطل';
            classesSpan.textContent = container.className || 'لا يوجد';
            
            console.log('Container classes:', container.className);
            console.log('Has animate-button:', hasAnimateClass);
        }
        
        function enableAnimation() {
            const container = document.getElementById('form-container');
            container.classList.add('animate-button');
            updateStatus();
            console.log('Animation enabled');
        }
        
        function disableAnimation() {
            const container = document.getElementById('form-container');
            container.classList.remove('animate-button');
            updateStatus();
            console.log('Animation disabled');
        }
        
        // Initial status
        updateStatus();
        
        // Auto-enable for testing
        setTimeout(() => {
            enableAnimation();
        }, 1000);
    </script>
</body>
</html>
