<?php
/**
 * Debug file to check animation settings
 */

// Check if we're in WordPress environment
if (!defined('ABSPATH')) {
    // If not, try to load WordPress
    $wp_load_paths = [
        '../../../wp-load.php',
        '../../../../wp-load.php',
        '../../../../../wp-load.php'
    ];
    
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            break;
        }
    }
}

if (!defined('ABSPATH')) {
    die('WordPress not found');
}

echo "<h1>تشخيص إعدادات الأنيميشن</h1>";

// Check current animation setting
$animate_setting = get_option('rid_cod_animate_submit_button', 'not_set');
echo "<h2>الإعداد الحالي:</h2>";
echo "<p><strong>rid_cod_animate_submit_button:</strong> " . $animate_setting . "</p>";

// Check old setting for comparison
$old_setting = get_option('rid_cod_second_form_animate_button', 'not_set');
echo "<p><strong>rid_cod_second_form_animate_button (القديم):</strong> " . $old_setting . "</p>";

// Check form style
$form_style = get_option('rid_cod_form_style', 'classic');
echo "<p><strong>نوع النموذج:</strong> " . $form_style . "</p>";

// Test the logic
echo "<h2>اختبار المنطق:</h2>";
if ($animate_setting === '1') {
    echo "<p style='color: green;'>✅ الأنيميشن مفعل - يجب أن يظهر كلاس animate-button</p>";
} else {
    echo "<p style='color: red;'>❌ الأنيميشن معطل أو غير محدد</p>";
}

// Show all RID COD options
echo "<h2>جميع إعدادات RID COD:</h2>";
$all_options = wp_load_alloptions();
$rid_options = [];
foreach ($all_options as $key => $value) {
    if (strpos($key, 'rid_cod') === 0) {
        $rid_options[$key] = $value;
    }
}

echo "<pre>";
foreach ($rid_options as $key => $value) {
    echo $key . " = " . $value . "\n";
}
echo "</pre>";

// Test form class generation
echo "<h2>اختبار توليد كلاس النموذج:</h2>";
$form_class = 'rid-cod-form-' . $form_style;
if ($animate_setting === '1') {
    $form_class .= ' animate-button';
}
echo "<p><strong>الكلاس المتوقع:</strong> " . $form_class . "</p>";

// CSS Test
echo "<h2>اختبار CSS:</h2>";
echo "<style>
.test-button {
    padding: 10px 20px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 5px;
    margin: 10px;
}
.animate-button .test-button {
    animation: shaking 1.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
    transform: translate3d(0, 0, 0);
}
@keyframes shaking {
    0%, 100% { transform: translate3d(0, 0, 0); }
    4%, 46% { transform: translate3d(-1px, 0, 0); }
    8%, 42% { transform: translate3d(2px, 0, 0); }
    12%, 37% { transform: translate3d(-3px, 0, 0); }
    16%, 33% { transform: translate3d(3px, 0, 0); }
}
</style>";

echo "<div class='animate-button'>";
echo "<button class='test-button'>زر اختبار مع أنيميشن</button>";
echo "</div>";

echo "<div>";
echo "<button class='test-button'>زر اختبار بدون أنيميشن</button>";
echo "</div>";

// Update setting for testing
if (isset($_GET['enable'])) {
    update_option('rid_cod_animate_submit_button', '1');
    echo "<p style='color: green;'>تم تفعيل الأنيميشن</p>";
    echo "<script>setTimeout(() => location.reload(), 1000);</script>";
}

if (isset($_GET['disable'])) {
    update_option('rid_cod_animate_submit_button', '0');
    echo "<p style='color: red;'>تم إلغاء الأنيميشن</p>";
    echo "<script>setTimeout(() => location.reload(), 1000);</script>";
}

echo "<h2>أدوات التحكم:</h2>";
echo "<a href='?enable=1' style='background: green; color: white; padding: 10px; text-decoration: none; margin: 5px;'>تفعيل الأنيميشن</a>";
echo "<a href='?disable=1' style='background: red; color: white; padding: 10px; text-decoration: none; margin: 5px;'>إلغاء الأنيميشن</a>";
echo "<a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none; margin: 5px;'>تحديث الصفحة</a>";
?>
